name: TeamWorkFlow CI/CD Pipeline

on:
  push:
    branches: [ "master", "development" ]
  pull_request:
    branches: [ "master", "development" ]

jobs:
  unit-tests:
    name: "Unit Tests"
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup .NET 6.0
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 6.0.x

    - name: Restore dependencies
      run: dotnet restore

    - name: Build solution
      run: dotnet build

    - name: Run Unit Tests
      run: dotnet test UnitTests/UnitTests.csproj --no-build
      env:
        USE_IN_MEMORY_DATABASE: true

  integration-tests:
    name: "Integration Tests (Playwright)"
    runs-on: ubuntu-latest
    needs: unit-tests

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup .NET 6.0
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 6.0.x

    - name: Restore dependencies
      run: dotnet restore

    - name: Build solution
      run: dotnet build

    - name: Install Playwright
      run: pwsh TeamWorkFlow.PlaywrightTests/bin/Debug/net6.0/playwright.ps1 install chromium

    - name: Start ASP.NET Core app in background
      run: |
        dotnet run --project TeamWorkFlow/TeamWorkFlow.csproj --urls "http://localhost:7015" &
        echo "Waiting for app to start..."
        sleep 10  # adjust if needed
      env:
        USE_IN_MEMORY_DATABASE: true

    - name: Run Playwright Integration Tests
      run: dotnet test TeamWorkFlow.PlaywrightTests/TeamWorkFlow.PlaywrightTests.csproj
      env:
        USE_IN_MEMORY_DATABASE: true
        CI: true
        APP_BASE_URL: http://localhost:7015
