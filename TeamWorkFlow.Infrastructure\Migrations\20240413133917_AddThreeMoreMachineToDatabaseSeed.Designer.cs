﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using TeamWorkFlow.Infrastructure.Data;

#nullable disable

namespace TeamWorkFlow.Infrastructure.Migrations
{
    [DbContext(typeof(TeamWorkFlowDbContext))]
    [Migration("20240413133917_AddThreeMoreMachineToDatabaseSeed")]
    partial class AddThreeMoreMachineToDatabaseSeed
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.27")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder, 1L, 1);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);

                    b.HasData(
                        new
                        {
                            Id = "b806eee6-2ceb-4956-9643-e2e2e82289d2",
                            AccessFailedCount = 0,
                            ConcurrencyStamp = "d9d19e80-610f-44db-ba6f-0ba6b4b4b2c9",
                            Email = "<EMAIL>",
                            EmailConfirmed = false,
                            LockoutEnabled = false,
                            NormalizedEmail = "<EMAIL>",
                            NormalizedUserName = "<EMAIL>",
                            PasswordHash = "AQAAAAEAACcQAAAAEE9yVdjVGNdo9Q5lF5lxyD51yalW+rNN3sIwvoiK+/6vNUd7xwsimNWpeX6JwD82KA==",
                            PhoneNumberConfirmed = false,
                            SecurityStamp = "341fba96-2787-4380-900c-fd0b81ff8c94",
                            TwoFactorEnabled = false,
                            UserName = "<EMAIL>"
                        },
                        new
                        {
                            Id = "7bf9623c-54d9-45ba-84c6-52806dcee7bd",
                            AccessFailedCount = 0,
                            ConcurrencyStamp = "062f86d9-8e6b-4faa-9481-ee9bf011df9a",
                            Email = "<EMAIL>",
                            EmailConfirmed = false,
                            LockoutEnabled = false,
                            NormalizedEmail = "<EMAIL>",
                            NormalizedUserName = "<EMAIL>",
                            PasswordHash = "AQAAAAEAACcQAAAAEAF6MhR8xsHOmQ5rtR3pAk4Ssm7GOeGw9GB6nRMFtcuqpQIUFV+Rd6VkcKaa7sTeFw==",
                            PhoneNumberConfirmed = false,
                            SecurityStamp = "ef45f4ce-9f34-46ca-9d0e-3833ce4a7c2f",
                            TwoFactorEnabled = false,
                            UserName = "<EMAIL>"
                        },
                        new
                        {
                            Id = "cf41999b-9cad-4b75-977d-a2fdb3d02e77",
                            AccessFailedCount = 0,
                            ConcurrencyStamp = "f6cf4375-e30e-4a8a-8b4c-f0fb723a7a57",
                            Email = "<EMAIL>",
                            EmailConfirmed = false,
                            LockoutEnabled = false,
                            NormalizedEmail = "<EMAIL>",
                            NormalizedUserName = "<EMAIL>",
                            PasswordHash = "AQAAAAEAACcQAAAAEJ3aW/wj+0gRkMMP18NKfscyjbixAMok0gn6+IB76KE1o8X1wHRQ8TLou2sPM7djFw==",
                            PhoneNumberConfirmed = false,
                            SecurityStamp = "73af7f97-6410-4d5b-93a4-f057c89712c3",
                            TwoFactorEnabled = false,
                            UserName = "<EMAIL>"
                        });
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 3,
                            ClaimType = "userName",
                            ClaimValue = "<EMAIL>",
                            UserId = "b806eee6-2ceb-4956-9643-e2e2e82289d2"
                        },
                        new
                        {
                            Id = 2,
                            ClaimType = "userName",
                            ClaimValue = "<EMAIL>",
                            UserId = "7bf9623c-54d9-45ba-84c6-52806dcee7bd"
                        },
                        new
                        {
                            Id = 1,
                            ClaimType = "userName",
                            ClaimValue = "<EMAIL>",
                            UserId = "cf41999b-9cad-4b75-977d-a2fdb3d02e77"
                        });
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("ProviderKey")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.Machine", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasComment("Machine identifier");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<DateTime>("CalibrationSchedule")
                        .HasColumnType("datetime2")
                        .HasComment("Machine calibration schedule");

                    b.Property<int>("Capacity")
                        .HasColumnType("int")
                        .HasComment("Machine capacity");

                    b.Property<string>("ImageUrl")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)")
                        .HasComment("Machine picture");

                    b.Property<bool>("IsCalibrated")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("MaintenanceScheduleEndDate")
                        .HasColumnType("datetime2")
                        .HasComment("Machine maintenanceScheduleEndDate");

                    b.Property<DateTime?>("MaintenanceScheduleStartDate")
                        .HasColumnType("datetime2")
                        .HasComment("Machine maintenanceScheduleStartDate");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("Machine name");

                    b.Property<double>("TotalMachineLoad")
                        .HasColumnType("float")
                        .HasComment("Machine total load");

                    b.HasKey("Id");

                    b.ToTable("Machines");

                    b.HasComment("Machine db model");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CalibrationSchedule = new DateTime(2024, 4, 4, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Capacity = 20,
                            ImageUrl = "https://www.researchgate.net/profile/Nermina_Zaimovic-Uzunovic2/publication/343880067/figure/fig2/AS:928740968255491@1598440510374/Measurement-of-the-top-surface-Fig4-CMM-Zeiss-Contura-G2_Q320.jpg",
                            IsCalibrated = false,
                            Name = "Zeiss Contura",
                            TotalMachineLoad = 0.0
                        },
                        new
                        {
                            Id = 2,
                            CalibrationSchedule = new DateTime(2024, 4, 4, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Capacity = 20,
                            ImageUrl = "https://www.qpluslabs.com/wp-content/uploads/2019/11/Zeiss-O-Inspect-863-475px.jpg",
                            IsCalibrated = false,
                            Name = "Zeiss O-inspect",
                            TotalMachineLoad = 0.0
                        },
                        new
                        {
                            Id = 3,
                            CalibrationSchedule = new DateTime(2024, 4, 4, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Capacity = 20,
                            ImageUrl = "https://i0.wp.com/metrology.news/wp-content/uploads/2023/02/ZEISS-METROTOM-1.jpg?resize=450%2C404",
                            IsCalibrated = false,
                            Name = "Zeiss Metrotom",
                            TotalMachineLoad = 0.0
                        },
                        new
                        {
                            Id = 4,
                            CalibrationSchedule = new DateTime(2024, 6, 6, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Capacity = 16,
                            ImageUrl = "https://www.zeiss.com/content/dam/metrology/products/systems/ct/bosello-new/bosello-sre-max.jpg",
                            IsCalibrated = true,
                            Name = "Zeiss X-ray",
                            TotalMachineLoad = 0.0
                        },
                        new
                        {
                            Id = 5,
                            CalibrationSchedule = new DateTime(2024, 6, 6, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Capacity = 20,
                            ImageUrl = "https://measuremetrology.com/wp-content/uploads/2023/03/mitutoyobrightapex504.png",
                            IsCalibrated = true,
                            Name = "Mitutoyo Scan",
                            TotalMachineLoad = 0.0
                        },
                        new
                        {
                            Id = 6,
                            CalibrationSchedule = new DateTime(2024, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Capacity = 11,
                            ImageUrl = "https://www.micro-shop.zeiss.com/data/image/shop-catalog-system/group_6038.jpg",
                            IsCalibrated = true,
                            Name = "Zeiss Microscope E9000",
                            TotalMachineLoad = 0.0
                        });
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.Operator", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasComment("Operator identifier");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("AvailabilityStatusId")
                        .HasColumnType("int")
                        .HasComment("Operator status identifier");

                    b.Property<int>("Capacity")
                        .HasColumnType("int")
                        .HasComment("Operator working capacity in hours per day/shift");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasComment("First and Last name of the operator");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasComment("Showing if the current operator is still working in the company");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasComment("Operator phoneNumber");

                    b.HasKey("Id");

                    b.HasIndex("AvailabilityStatusId");

                    b.ToTable("Operators");

                    b.HasComment("Operator DB model");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            AvailabilityStatusId = 4,
                            Capacity = 8,
                            Email = "<EMAIL>",
                            FullName = "Aleksandar Paytalov",
                            IsActive = true,
                            PhoneNumber = "+359881234567"
                        },
                        new
                        {
                            Id = 2,
                            AvailabilityStatusId = 1,
                            Capacity = 4,
                            Email = "<EMAIL>",
                            FullName = "Jon Doe",
                            IsActive = true,
                            PhoneNumber = "+359887654321"
                        },
                        new
                        {
                            Id = 3,
                            AvailabilityStatusId = 2,
                            Capacity = 8,
                            Email = "<EMAIL>",
                            FullName = "Jane Doe",
                            IsActive = true,
                            PhoneNumber = "+359894567890"
                        });
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.OperatorAvailabilityStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasComment("Operator identifier");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)")
                        .HasComment("Availability status name");

                    b.HasKey("Id");

                    b.ToTable("OperatorAvailabilityStatusEnumerable");

                    b.HasComment("Operator availability status db model");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "at work"
                        },
                        new
                        {
                            Id = 2,
                            Name = "in sick leave"
                        },
                        new
                        {
                            Id = 3,
                            Name = "on vacation"
                        },
                        new
                        {
                            Id = 4,
                            Name = "on training"
                        });
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.Part", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasComment("Part identifier");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("ImageUrl")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)")
                        .HasComment("Part name");

                    b.Property<string>("PartArticleNumber")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasComment("Part article number");

                    b.Property<string>("PartClientNumber")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasComment("Client article number for the current part");

                    b.Property<string>("PartModel")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("PartStatusId")
                        .HasColumnType("int")
                        .HasComment("PartStatus identifier");

                    b.Property<int>("ProjectId")
                        .HasColumnType("int");

                    b.Property<int>("ToolNumber")
                        .HasColumnType("int")
                        .HasComment("Part tool number");

                    b.HasKey("Id");

                    b.HasIndex("PartStatusId");

                    b.HasIndex("ProjectId");

                    b.ToTable("Parts");

                    b.HasComment("Part Db model");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            ImageUrl = "https://www.preh.com/fileadmin/templates/website/media/images/Produkte/Car_HMI/Climate_Control/Preh_Produkte_Climate_Control_AudiA1.jpg",
                            Name = "VW Housing Front D9",
                            PartArticleNumber = "2.4.100.501",
                            PartClientNumber = "252.166-15",
                            PartModel = "252.166-15_0B_VW Housing Front D9",
                            PartStatusId = 2,
                            ProjectId = 2,
                            ToolNumber = 9055
                        },
                        new
                        {
                            Id = 2,
                            ImageUrl = "https://wodofogdr.com/cdn/shop/products/GDR-MBT-823287-2_grande.jpg?v=1626163358",
                            Name = "VW Housing D8",
                            PartArticleNumber = "2.4.100.502",
                            PartClientNumber = "252.167-00",
                            PartModel = "252.167-00_0D_VW Housing D8",
                            PartStatusId = 2,
                            ProjectId = 2,
                            ToolNumber = 3418
                        },
                        new
                        {
                            Id = 3,
                            ImageUrl = "https://wodofogdr.com/cdn/shop/products/GDR-MBT-823287-2_grande.jpg?v=1626163358",
                            Name = "Audi Housing A5 X-line",
                            PartArticleNumber = "2.4.100.605",
                            PartClientNumber = "312.205-11",
                            PartModel = "334.255-10_0E_Audi Housing A5 X-line",
                            PartStatusId = 1,
                            ProjectId = 2,
                            ToolNumber = 3459
                        },
                        new
                        {
                            Id = 4,
                            ImageUrl = "https://www.bhtc.com/media/pages/produkte/fahrzeugklimatisierung/bmw-klimabediengerat/3086657772-1542633776/bmw_klimabediengeraet_gkl.png",
                            Name = "Toyota Housing F5",
                            PartArticleNumber = "2.4.202.333",
                            PartClientNumber = "212.200-00",
                            PartModel = "212.200-00_0B_Toyota Housing F5",
                            PartStatusId = 3,
                            ProjectId = 3,
                            ToolNumber = 5533
                        },
                        new
                        {
                            Id = 5,
                            ImageUrl = "https://conti-engineering.com/wp-content/uploads/2020/09/climatecontrol_beitrag.jpg",
                            Name = "BMW Front-Back Panels X5",
                            PartArticleNumber = "2.3.105.603",
                            PartClientNumber = "212.200-11",
                            PartModel = "212.200-11_0E_BMW Front-Back Panels X5",
                            PartStatusId = 3,
                            ProjectId = 1,
                            ToolNumber = 3360
                        },
                        new
                        {
                            Id = 6,
                            ImageUrl = "https://www.preh.com/fileadmin/templates/website/media/images/Produkte/Car_HMI/Climate_Control/Preh_Produkte_Climate_Control_FordFocus.jpg",
                            Name = "VW Tuareg Housing panel G5",
                            PartArticleNumber = "2.4.305.777",
                            PartClientNumber = "431.222-07",
                            PartModel = "431.222-07_0A_VW Tuareg Housing panel G5",
                            PartStatusId = 1,
                            ProjectId = 2,
                            ToolNumber = 2515
                        },
                        new
                        {
                            Id = 7,
                            ImageUrl = "https://www.preh.com/fileadmin/templates/website/media/images/Produkte/Car_HMI/Climate_Control/Preh_Produkte_Climate_Control_AudiR8.jpg",
                            Name = "Toyota Aventis Housing Klima module V6",
                            PartArticleNumber = "2.4.105.589",
                            PartClientNumber = "305.201-11",
                            PartModel = "305.201-11_0B_Toyota Aventis Housing Klima module V6",
                            PartStatusId = 1,
                            ProjectId = 3,
                            ToolNumber = 9999
                        },
                        new
                        {
                            Id = 8,
                            ImageUrl = "https://autoprotoway.com/wp-content/uploads/2022/09/precision-automotive-lighting-parts.jpg",
                            Name = "VW Light Conductor Front Panel",
                            PartArticleNumber = "2.4.222.777",
                            PartClientNumber = "213.891-22",
                            PartModel = "213.891-22_0T_VW Light Conductor Front Panel",
                            PartStatusId = 1,
                            ProjectId = 2,
                            ToolNumber = 9995
                        });
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.PartStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasComment("PartStatus identifier");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)")
                        .HasComment("PartStatus name");

                    b.HasKey("Id");

                    b.ToTable("PartStatusEnumerable");

                    b.HasComment("Part status Db model");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "released"
                        },
                        new
                        {
                            Id = 2,
                            Name = "not released"
                        },
                        new
                        {
                            Id = 3,
                            Name = "Conditional released"
                        });
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.Priority", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasComment("Priority identifier");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)")
                        .HasComment("Priority name");

                    b.HasKey("Id");

                    b.ToTable("Priorities");

                    b.HasComment("Priority data model");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "low"
                        },
                        new
                        {
                            Id = 2,
                            Name = "normal"
                        },
                        new
                        {
                            Id = 3,
                            Name = "high"
                        });
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.Project", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasComment("Project identifier");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Appliance")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasComment("Project appliance sector");

                    b.Property<string>("ClientName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasComment("Client name");

                    b.Property<string>("ProjectName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasComment("Project name");

                    b.Property<string>("ProjectNumber")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasComment("Project number");

                    b.Property<int>("ProjectStatusId")
                        .HasColumnType("int")
                        .HasComment("ProjectStatus identifier");

                    b.Property<int>("TotalHoursSpent")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProjectStatusId");

                    b.ToTable("Projects");

                    b.HasComment("Project data model");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Appliance = "Automotive industry",
                            ClientName = "Bmw",
                            ProjectName = "BMW Housing Gx9",
                            ProjectNumber = "249100",
                            ProjectStatusId = 1,
                            TotalHoursSpent = 50
                        },
                        new
                        {
                            Id = 2,
                            Appliance = "Automotive industry",
                            ClientName = "Vw",
                            ProjectName = "Vw Tuareg Front panel ",
                            ProjectNumber = "249200",
                            ProjectStatusId = 2,
                            TotalHoursSpent = 20
                        },
                        new
                        {
                            Id = 3,
                            Appliance = "Automotive industry",
                            ClientName = "Toyota",
                            ProjectName = "Toyota Climatic module X5",
                            ProjectNumber = "249300",
                            ProjectStatusId = 3,
                            TotalHoursSpent = 41
                        });
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.ProjectStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasComment("ProjectStatus identifier");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasComment("ProjectStatus name");

                    b.HasKey("Id");

                    b.ToTable("ProjectStatusEnumerable");

                    b.HasComment("ProjectStatus data model");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "In production"
                        },
                        new
                        {
                            Id = 2,
                            Name = "In development"
                        },
                        new
                        {
                            Id = 3,
                            Name = "in ACL"
                        });
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.Task", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasComment("Task identifier");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Attachment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasComment("Task attachments - files, drawings, documents, etc.");

                    b.Property<string>("Comment")
                        .HasMaxLength(2500)
                        .HasColumnType("nvarchar(2500)")
                        .HasComment("Comment for the current task");

                    b.Property<string>("CreatorId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)")
                        .HasComment("Task creator identifier");

                    b.Property<DateTime?>("DeadLine")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1500)
                        .HasColumnType("nvarchar(1500)")
                        .HasComment("Task description");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2")
                        .HasComment("The date when the task is finished");

                    b.Property<int>("EstimatedTime")
                        .HasColumnType("int")
                        .HasComment("Estimated time for the Task that is needed to be complete - in hours");

                    b.Property<int?>("MachineId")
                        .HasColumnType("int")
                        .HasComment("Machine identifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasComment("Task Name");

                    b.Property<int>("PriorityId")
                        .HasColumnType("int")
                        .HasComment("Priority identifier");

                    b.Property<int>("ProjectId")
                        .HasColumnType("int");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2")
                        .HasComment("Task starting date");

                    b.Property<int>("TaskStatusId")
                        .HasColumnType("int")
                        .HasComment("TaskStatus identifier");

                    b.HasKey("Id");

                    b.HasIndex("CreatorId");

                    b.HasIndex("MachineId");

                    b.HasIndex("PriorityId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("TaskStatusId");

                    b.ToTable("Tasks");

                    b.HasComment("Task Db model");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatorId = "cf41999b-9cad-4b75-977d-a2fdb3d02e77",
                            DeadLine = new DateTime(2023, 12, 12, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "LOP dimensional report for phase 1 (T0) - samples from the tool maker should arrive in Calendar week 48.",
                            EstimatedTime = 25,
                            MachineId = 1,
                            Name = "Housing Front Panel - LOP.",
                            PriorityId = 2,
                            ProjectId = 2,
                            StartDate = new DateTime(2023, 11, 3, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TaskStatusId = 1
                        },
                        new
                        {
                            Id = 2,
                            CreatorId = "cf41999b-9cad-4b75-977d-a2fdb3d02e77",
                            DeadLine = new DateTime(2024, 7, 7, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "PPAP level 3",
                            EstimatedTime = 32,
                            MachineId = 2,
                            Name = "Housing Klima - PPAP",
                            PriorityId = 2,
                            ProjectId = 3,
                            StartDate = new DateTime(2024, 6, 6, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TaskStatusId = 2
                        },
                        new
                        {
                            Id = 3,
                            CreatorId = "cf41999b-9cad-4b75-977d-a2fdb3d02e77",
                            DeadLine = new DateTime(2024, 7, 7, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Full PPAP documents need to be created and prepared for sending to customer no late than 07.07.2024.",
                            EstimatedTime = 32,
                            Name = "Housing D8 - PPAP",
                            PriorityId = 2,
                            ProjectId = 2,
                            StartDate = new DateTime(2024, 6, 6, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TaskStatusId = 2
                        },
                        new
                        {
                            Id = 4,
                            CreatorId = "cf41999b-9cad-4b75-977d-a2fdb3d02e77",
                            Description = "Validation of the part on another production machine. Full dimensional report of 5 shots from the new machine. Results must be compared with measurements of the part from the serial (validated) production machine",
                            EstimatedTime = 8,
                            Name = "BMW Back Panel - Sample order no. 987",
                            PriorityId = 2,
                            ProjectId = 1,
                            StartDate = new DateTime(2024, 7, 18, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TaskStatusId = 1
                        },
                        new
                        {
                            Id = 5,
                            CreatorId = "7bf9623c-54d9-45ba-84c6-52806dcee7bd",
                            Description = "Validation of the part on another production machine. Full dimensional report of 5 shots from the new machine. Results must be compared with measurements of the part from the serial (validated) production machine",
                            EndDate = new DateTime(2024, 7, 12, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            EstimatedTime = 10,
                            MachineId = 2,
                            Name = "BMW Front panel - Sample order No. 954",
                            PriorityId = 1,
                            ProjectId = 1,
                            StartDate = new DateTime(2024, 6, 6, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TaskStatusId = 3
                        },
                        new
                        {
                            Id = 6,
                            CreatorId = "7bf9623c-54d9-45ba-84c6-52806dcee7bd",
                            DeadLine = new DateTime(2024, 6, 12, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "PPAP documents level 3 must be performed. Note: Deviations on dimensions 10 and 150 have been accepted from the customer. Drawing will be adjusted with next PPAP revision",
                            EndDate = new DateTime(2024, 6, 12, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            EstimatedTime = 16,
                            MachineId = 3,
                            Name = "Housing Klima module V6 - PPAP",
                            PriorityId = 3,
                            ProjectId = 3,
                            StartDate = new DateTime(2024, 6, 6, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TaskStatusId = 3
                        });
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.TaskOperator", b =>
                {
                    b.Property<int>("OperatorId")
                        .HasColumnType("int");

                    b.Property<int>("TaskId")
                        .HasColumnType("int");

                    b.HasKey("OperatorId", "TaskId");

                    b.HasIndex("TaskId");

                    b.ToTable("TasksOperators");

                    b.HasComment("TaskOperator data model");
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.TaskStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasComment("TaskStatus identifier");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasComment("TaskStatus name");

                    b.HasKey("Id");

                    b.ToTable("TaskStatusEnumerable");

                    b.HasComment("TaskStatus data model");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "open"
                        },
                        new
                        {
                            Id = 2,
                            Name = "in progress"
                        },
                        new
                        {
                            Id = 3,
                            Name = "finished"
                        },
                        new
                        {
                            Id = 4,
                            Name = "canceled"
                        });
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.Operator", b =>
                {
                    b.HasOne("TeamWorkFlow.Infrastructure.Data.Models.OperatorAvailabilityStatus", "AvailabilityStatus")
                        .WithMany("Operators")
                        .HasForeignKey("AvailabilityStatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AvailabilityStatus");
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.Part", b =>
                {
                    b.HasOne("TeamWorkFlow.Infrastructure.Data.Models.PartStatus", "PartStatus")
                        .WithMany("Parts")
                        .HasForeignKey("PartStatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TeamWorkFlow.Infrastructure.Data.Models.Project", "Project")
                        .WithMany("Parts")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PartStatus");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.Project", b =>
                {
                    b.HasOne("TeamWorkFlow.Infrastructure.Data.Models.ProjectStatus", "ProjectStatus")
                        .WithMany("Tasks")
                        .HasForeignKey("ProjectStatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProjectStatus");
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.Task", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", "Creator")
                        .WithMany()
                        .HasForeignKey("CreatorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TeamWorkFlow.Infrastructure.Data.Models.Machine", "Machine")
                        .WithMany("Tasks")
                        .HasForeignKey("MachineId");

                    b.HasOne("TeamWorkFlow.Infrastructure.Data.Models.Priority", "Priority")
                        .WithMany("Tasks")
                        .HasForeignKey("PriorityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TeamWorkFlow.Infrastructure.Data.Models.Project", "Project")
                        .WithMany()
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TeamWorkFlow.Infrastructure.Data.Models.TaskStatus", "TaskStatus")
                        .WithMany("Tasks")
                        .HasForeignKey("TaskStatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Creator");

                    b.Navigation("Machine");

                    b.Navigation("Priority");

                    b.Navigation("Project");

                    b.Navigation("TaskStatus");
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.TaskOperator", b =>
                {
                    b.HasOne("TeamWorkFlow.Infrastructure.Data.Models.Operator", "Operator")
                        .WithMany("TasksOperators")
                        .HasForeignKey("OperatorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TeamWorkFlow.Infrastructure.Data.Models.Task", "Task")
                        .WithMany("TasksOperators")
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Operator");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.Machine", b =>
                {
                    b.Navigation("Tasks");
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.Operator", b =>
                {
                    b.Navigation("TasksOperators");
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.OperatorAvailabilityStatus", b =>
                {
                    b.Navigation("Operators");
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.PartStatus", b =>
                {
                    b.Navigation("Parts");
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.Priority", b =>
                {
                    b.Navigation("Tasks");
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.Project", b =>
                {
                    b.Navigation("Parts");
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.ProjectStatus", b =>
                {
                    b.Navigation("Tasks");
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.Task", b =>
                {
                    b.Navigation("TasksOperators");
                });

            modelBuilder.Entity("TeamWorkFlow.Infrastructure.Data.Models.TaskStatus", b =>
                {
                    b.Navigation("Tasks");
                });
#pragma warning restore 612, 618
        }
    }
}
