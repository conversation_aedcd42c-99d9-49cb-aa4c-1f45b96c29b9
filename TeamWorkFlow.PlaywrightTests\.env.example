# TeamWorkFlow Playwright Test Environment Variables
# Copy this file to .env and fill in your test credentials
# NEVER commit the .env file to source control!

# Test Admin User Credentials
TEST_ADMIN_EMAIL=<EMAIL>
TEST_ADMIN_PASSWORD=YourSecureAdminPassword123!

# Test Operator User Credentials  
TEST_OPERATOR_EMAIL=<EMAIL>
TEST_OPERATOR_PASSWORD=YourSecureOperatorPassword123!

# Optional: Override base URL for tests
# TEST_BASE_URL=https://localhost:7015

# Security Notes:
# - Use test-specific accounts only, never production accounts
# - These credentials should exist in your test database
# - Consider using different passwords than shown in examples
# - For CI/CD, set these as pipeline secrets instead of using .env files
