{"TestSettings": {"BaseUrl": "https://localhost:7015", "Timeout": 30000, "DefaultWaitTime": 5000, "ScreenshotOnFailure": true, "VideoOnFailure": true, "SlowMo": 0}, "TestUsers": {"AdminUser": {"Email": "<EMAIL>", "Password": "FakeAdminPass123!", "FirstName": "Fake", "LastName": "Admin"}, "OperatorUser": {"Email": "<EMAIL>", "Password": "FakeOperatorPass456!", "FirstName": "Fake", "LastName": "Operator"}}, "TestData": {"SampleTask": {"Name": "Test Task - Automated", "Description": "This is a test task created by automated tests", "ProjectNumber": "TEST001"}, "SampleProject": {"Name": "Test Project - Automated", "ProjectNumber": "PROJ001", "TotalHoursSpent": 40}, "SampleMachine": {"Name": "Test Machine - Automated", "Capacity": 100, "ImageUrl": "https://example.com/test-machine.jpg"}}}