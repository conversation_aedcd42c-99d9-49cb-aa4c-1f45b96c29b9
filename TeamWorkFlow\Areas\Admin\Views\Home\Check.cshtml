﻿@{
	ViewData["Title"] = "Admin Dashboard";
}

@section Styles {
	<link rel="stylesheet" href="~/css/admin/admin.css" asp-append-version="true" />
}

<div class="admin-container">
	<!-- Admin Header -->
	<div class="admin-header">
		<h1 class="admin-title">Admin Dashboard</h1>
		<p class="admin-subtitle">Comprehensive management and oversight of your TeamWorkFlow system</p>
		<span class="admin-badge">Administrator Access</span>
	</div>

	<!-- Dashboard Cards -->
	<div class="dashboard-grid">
		<!-- Operator Management Card -->
		<div class="dashboard-card">
			<div class="dashboard-card-icon">
				<svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
				</svg>
			</div>
			<h3 class="dashboard-card-title">Operator Management</h3>
			<p class="dashboard-card-description">Manage all operators, activate/deactivate accounts, and oversee operator assignments and capabilities.</p>
			<a asp-area="Admin" asp-controller="Operator" asp-action="Activate" class="dashboard-card-action">
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-1M10 6V5a2 2 0 112 0v1M10 6h4"></path>
				</svg>
				Manage Operators
			</a>
		</div>

		<!-- Active Operators Card -->
		<div class="dashboard-card">
			<div class="dashboard-card-icon">
				<svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
				</svg>
			</div>
			<h3 class="dashboard-card-title">Active Operators</h3>
			<p class="dashboard-card-description">View currently active operators, their availability status, and real-time capacity information.</p>
			<a asp-area="" asp-controller="Operator" asp-action="All" class="dashboard-card-action">
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
				</svg>
				View Active
			</a>
		</div>

		<!-- Task Management Card -->
		<div class="dashboard-card">
			<div class="dashboard-card-icon">
				<svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
				</svg>
			</div>
			<h3 class="dashboard-card-title">Assigned Tasks</h3>
			<p class="dashboard-card-description">Monitor all assigned tasks, track progress, and manage task assignments across the organization.</p>
			<a asp-area="Admin" asp-controller="Task" asp-action="AllAssigns" class="dashboard-card-action">
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
				</svg>
				Manage Tasks
			</a>
		</div>

		<!-- All Operators Card -->
		<div class="dashboard-card">
			<div class="dashboard-card-icon">
				<svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
				</svg>
			</div>
			<h3 class="dashboard-card-title">All Operators</h3>
			<p class="dashboard-card-description">Complete operator directory with detailed profiles, contact information, and administrative controls.</p>
			<a asp-area="Admin" asp-controller="Operator" asp-action="All" class="dashboard-card-action">
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
				</svg>
				View All
			</a>
		</div>

		<!-- User Role Management Card -->
		<div class="dashboard-card">
			<div class="dashboard-card-icon">
				<svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
				</svg>
			</div>
			<h3 class="dashboard-card-title">User Role Management</h3>
			<p class="dashboard-card-description">Manage user roles and permissions. Promote operators to administrators or demote administrators to operators.</p>
			<a asp-area="Admin" asp-controller="UserRole" asp-action="Index" class="dashboard-card-action">
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
				</svg>
				Manage Roles
			</a>
		</div>
	</div>
</div>

@section Scripts {
	<script src="~/js/admin/admin.js" asp-append-version="true"></script>
}
