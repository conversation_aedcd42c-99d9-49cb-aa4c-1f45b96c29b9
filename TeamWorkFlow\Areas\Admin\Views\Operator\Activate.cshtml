﻿@using TeamWorkFlow.Core.Extensions
@model ICollection<OperatorServiceModel>
@{
	ViewData["Title"] = "Operator Management";
}

@section Styles {
	<link rel="stylesheet" href="~/css/admin/admin.css" asp-append-version="true" />
}

<div class="admin-container">
	<!-- Admin Header -->
	<div class="admin-header">
		<h1 class="admin-title">Operator Management</h1>
		<p class="admin-subtitle">Activate and manage operator accounts to control system access</p>
		<span class="admin-badge">Account Management</span>
	</div>

	<!-- Success/Error Messages -->
	@if (TempData["SuccessMessage"] != null)
	{
		<div class="admin-alert admin-alert-success">
			<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
			</svg>
			@TempData["SuccessMessage"]
		</div>
	}
	@if (TempData["ErrorMessage"] != null)
	{
		<div class="admin-alert admin-alert-error">
			<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
			</svg>
			@TempData["ErrorMessage"]
		</div>
	}

	@if (Model.Any())
	{
		<!-- Operators Table Container -->
		<div class="admin-table-container">
			<div class="admin-table-header">
				<h2 class="admin-table-title">Inactive Operators Awaiting Activation</h2>
			</div>

			<div class="table-responsive">
				<table class="admin-table">
					<thead>
						<tr>
							<th>
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="display: inline; margin-right: 0.5rem;">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
								</svg>
								Full Name
							</th>
							<th>
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="display: inline; margin-right: 0.5rem;">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
								Availability Status
							</th>
							<th>
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="display: inline; margin-right: 0.5rem;">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
								</svg>
								Email
							</th>
							<th>
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="display: inline; margin-right: 0.5rem;">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
								</svg>
								Phone Number
							</th>
							<th>
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="display: inline; margin-right: 0.5rem;">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
								</svg>
								Capacity
							</th>
							<th>
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="display: inline; margin-right: 0.5rem;">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
								</svg>
								Actions
							</th>
						</tr>
					</thead>
					<tbody>
						@foreach (var o in Model)
						{
							<tr>
								<td>
									@{
										string initials = "";
										if (!string.IsNullOrEmpty(o.FullName))
										{
											var nameParts = o.FullName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
											initials = string.Join("", nameParts.Take(2).Select(part => part.Substring(0, 1).ToUpper()));
										}
									}
									<div style="display: flex; align-items: center; gap: 0.75rem;">
										<div style="width: 32px; height: 32px; border-radius: 50%; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.875rem;">
											@initials
										</div>
										<span style="font-weight: 600; color: #111827;">@o.FullName</span>
									</div>
								</td>
								<td>
									@{
										string statusClass = "status-inactive";
										if (o.AvailabilityStatus.ToLower().Contains("available") || o.AvailabilityStatus.ToLower().Contains("ready"))
										{
											statusClass = "status-active";
										}
									}
									<span class="@statusClass">
										<span class="status-indicator"></span>
										@o.AvailabilityStatus
									</span>
								</td>
								<td>
									<span style="color: #3b82f6; font-weight: 500;">@o.Email</span>
								</td>
								<td>
									<span style="color: #374151; font-family: monospace;">@o.PhoneNumber</span>
								</td>
								<td>
									@{
										int capacityPercentage = o.GetCapacityPercentage();
										string capacityColor = "#6b7280";
										if (capacityPercentage == 0)
										{
											capacityColor = "#9ca3af";
										}
										else if (capacityPercentage >= 80)
										{
											capacityColor = "#059669";
										}
										else if (capacityPercentage >= 50)
										{
											capacityColor = "#d97706";
										}
										else
										{
											capacityColor = "#dc2626";
										}
									}
									<span style="color: @capacityColor; font-weight: 600;" title="@o.Capacity hours per day">@o.GetCapacityDisplay()</span>
								</td>
								<td>
									@if (User.IsAdmin())
									{
										<form asp-controller="Operator" asp-area="Admin" asp-action="Activate" asp-route-id="@o.Id" method="post" style="display: inline;">
											<button type="submit" class="admin-btn admin-btn-success" data-tooltip="Activate this operator account">
												<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
												</svg>
												Activate
											</button>
										</form>
									}
								</td>
							</tr>
						}
					</tbody>
				</table>
			</div>
		</div>
	}
	else
	{
		<div class="admin-empty-state">
			<h2>No Inactive Operators</h2>
			<p>All operators are currently active. There are no accounts awaiting activation.</p>
			<a asp-area="Admin" asp-controller="Operator" asp-action="All" class="dashboard-card-action" style="margin-top: 1rem;">
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
				</svg>
				View All Operators
			</a>
		</div>
	}
</div>

@section Scripts {
	<script src="~/js/admin/admin.js" asp-append-version="true"></script>
	<partial name="_ValidationScriptsPartial" />
}
