﻿@model IEnumerable<OperatorAccessServiceModel>

@{
	ViewBag.Title = "All Operators";
}

@section Styles {
	<link rel="stylesheet" href="~/css/admin/admin.css" asp-append-version="true" />
}

<div class="admin-container">
	<!-- Admin Header -->
	<div class="admin-header">
		<h1 class="admin-title">All Operators</h1>
		<p class="admin-subtitle">Complete directory of all operators with their contact information and status</p>
		<span class="admin-badge">Operator Directory</span>
	</div>

	<!-- Success/Error Messages -->
	@if (TempData["SuccessMessage"] != null)
	{
		<div class="admin-alert admin-alert-success">
			<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
			</svg>
			@TempData["SuccessMessage"]
		</div>
	}
	@if (TempData["ErrorMessage"] != null)
	{
		<div class="admin-alert admin-alert-error">
			<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
			</svg>
			@TempData["ErrorMessage"]
		</div>
	}

	@if (Model.Any())
	{
		<!-- Operators Table Container -->
		<div class="admin-table-container">
			<div class="admin-table-header">
				<h2 class="admin-table-title">Operator Directory</h2>
			</div>

			<div class="table-responsive">
				<table class="admin-table">
					<thead>
						<tr>
							<th>
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="display: inline; margin-right: 0.5rem;">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
								</svg>
								Email Address
							</th>
							<th>
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="display: inline; margin-right: 0.5rem;">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
								</svg>
								Full Name
							</th>
							<th>
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="display: inline; margin-right: 0.5rem;">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
								</svg>
								Phone Number
							</th>
							<th>
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="display: inline; margin-right: 0.5rem;">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
								Status
							</th>
							<th style="text-align: center;">
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="display: inline; margin-right: 0.5rem;">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
								</svg>
								Actions
							</th>
						</tr>
					</thead>
					<tbody>
						@foreach (var op in Model)
						{
							<tr>
								<td>
									<span style="color: #3b82f6; font-weight: 500;">@op.Email</span>
								</td>
								<td>
									@{
										string initials = "";
										if (!string.IsNullOrEmpty(op.FullName))
										{
											var nameParts = op.FullName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
											initials = string.Join("", nameParts.Take(2).Select(part => part.Substring(0, 1).ToUpper()));
										}
									}
									<div style="display: flex; align-items: center; gap: 0.75rem;">
										<div style="width: 32px; height: 32px; border-radius: 50%; background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.875rem;">
											@initials
										</div>
										<span style="font-weight: 600; color: #111827;">@op.FullName</span>
									</div>
								</td>
								<td>
									<span style="color: #374151; font-family: monospace;">@op.PhoneNumber</span>
								</td>
								<td>
									@if (op.IsActive)
									{
										<span class="status-active">
											<span class="status-indicator"></span>
											Active (At Work)
										</span>
									}
									else
									{
										<span class="status-inactive">
											<span class="status-indicator"></span>
											Inactive (Unavailable)
										</span>
									}
								</td>
								<td style="text-align: center; padding: 0.75rem;">
									@if (op.IsActive)
									{
										<button type="button" class="admin-toggle-btn admin-toggle-btn-deactivate"
												title="Deactivate operator"
												onclick="showDeactivationModal(@op.Id, '@op.FullName')">
											<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
											</svg>
											Deactivate
										</button>
									}
									else
									{
										<form method="post" action="@Url.Action("ToggleStatus", "Operator", new { area = "Admin" })" style="display: inline;">
											<input type="hidden" name="id" value="@op.Id" />
											<button type="submit" class="admin-toggle-btn admin-toggle-btn-activate"
													title="Activate operator and set status to 'at work'"
													onclick="return confirm('Are you sure you want to activate @op.FullName? This will set their status to \\'at work\\' automatically.');">
												<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
												</svg>
												Activate
											</button>
										</form>
									}
								</td>
							</tr>
						}
					</tbody>
				</table>
			</div>
		</div>
	}
	else
	{
		<div class="admin-empty-state">
			<h2>No Operators Found</h2>
			<p>There are currently no operators registered in the system.</p>
			<a asp-area="" asp-controller="Operator" asp-action="Add" class="dashboard-card-action" style="margin-top: 1rem;">
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
				</svg>
				Add First Operator
			</a>
		</div>
	}
</div>

<!-- Deactivation Modal -->
<div id="deactivationModal" class="modal" style="display: none;">
	<div class="modal-content">
		<div class="modal-header">
			<h3>Deactivate Operator</h3>
			<span class="close" onclick="closeDeactivationModal()">&times;</span>
		</div>
		<div class="modal-body">
			<p>You are about to deactivate <strong id="operatorName"></strong>.</p>
			<p>Please select the availability status for this operator:</p>

			<form id="deactivationForm" method="post" action="@Url.Action("DeactivateWithStatus", "Operator", new { area = "Admin" })">
				@Html.AntiForgeryToken()
				<input type="hidden" id="operatorId" name="id" />

				<div class="form-group">
					<label for="availabilityStatusId">Availability Status:</label>
					<select id="availabilityStatusId" name="availabilityStatusId" class="form-control" required>
						<option value="">Select status...</option>
					</select>
				</div>

				<div class="modal-actions">
					<button type="button" class="btn btn-secondary" onclick="closeDeactivationModal()">Cancel</button>
					<button type="submit" class="btn btn-danger">Deactivate Operator</button>
				</div>
			</form>
		</div>
	</div>
</div>

@section Scripts {
	<script src="~/js/admin/admin.js" asp-append-version="true"></script>
	<script>
		let availabilityStatuses = [];
		let isSubmitting = false;

		// Load availability statuses when page loads
		document.addEventListener('DOMContentLoaded', function() {
			loadAvailabilityStatuses();

			// Add form submit handler to prevent double submission
			const form = document.getElementById('deactivationForm');
			if (form) {
				form.addEventListener('submit', function(e) {
					if (isSubmitting) {
						e.preventDefault();
						return false;
					}

					const statusSelect = document.getElementById('availabilityStatusId');
					if (!statusSelect.value) {
						e.preventDefault();
						alert('Please select an availability status.');
						return false;
					}

					isSubmitting = true;

					// Show loading state
					const submitBtn = form.querySelector('button[type="submit"]');
					if (submitBtn) {
						submitBtn.disabled = true;
						submitBtn.textContent = 'Processing...';
					}
				});
			}
		});

		async function loadAvailabilityStatuses() {
			try {
				const response = await fetch('@Url.Action("GetAvailabilityStatuses", "Operator", new { area = "Admin" })');
				if (response.ok) {
					availabilityStatuses = await response.json();
				}
			} catch (error) {
				console.error('Error loading availability statuses:', error);
			}
		}

		function showDeactivationModal(operatorId, operatorName) {
			// Reset form state
			isSubmitting = false;
			const form = document.getElementById('deactivationForm');
			if (form) {
				const submitBtn = form.querySelector('button[type="submit"]');
				if (submitBtn) {
					submitBtn.disabled = false;
					submitBtn.textContent = 'Deactivate Operator';
				}
			}

			document.getElementById('operatorId').value = operatorId;
			document.getElementById('operatorName').textContent = operatorName;

			// Populate status dropdown
			const statusSelect = document.getElementById('availabilityStatusId');
			statusSelect.innerHTML = '<option value="">Select status...</option>';

			availabilityStatuses.forEach(status => {
				const option = document.createElement('option');
				option.value = status.id;
				option.textContent = status.name;
				statusSelect.appendChild(option);
			});

			document.getElementById('deactivationModal').style.display = 'block';
		}

		function closeDeactivationModal() {
			// Reset form state when closing
			isSubmitting = false;
			const form = document.getElementById('deactivationForm');
			if (form) {
				const submitBtn = form.querySelector('button[type="submit"]');
				if (submitBtn) {
					submitBtn.disabled = false;
					submitBtn.textContent = 'Deactivate Operator';
				}
			}

			document.getElementById('deactivationModal').style.display = 'none';
		}

		// Close modal when clicking outside of it
		window.onclick = function(event) {
			const modal = document.getElementById('deactivationModal');
			if (event.target === modal) {
				closeDeactivationModal();
			}
		}
	</script>
}
