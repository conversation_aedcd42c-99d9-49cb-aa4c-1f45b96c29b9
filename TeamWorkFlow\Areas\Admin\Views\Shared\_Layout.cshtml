﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - TeamWorkFlow Admin</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/style.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/styles.css" />
    <link rel="stylesheet" href="~/css/admin/admin.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/admin/admin-navigation.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/TeamWorkFlow.styles.css" asp-append-version="true" />
    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm admin-nav border-bottom box-shadow mb-3">
            <div class="container-fluid">
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    @await Component.InvokeAsync(nameof(AdminMenuComponent))
	                <partial name="_AdminPartial"/>
                </div>
            </div>
        </nav>
    </header>

    <main role="main" class="pb-3">
        @RenderBody()
    </main>

    <footer class="admin-footer">
        <div class="container">
            &copy; @DateTime.Now.Year - TeamWorkFlow Admin Panel <span class="text-center"> - </span> <span class="text-info">Administrator Access Only</span>
        </div>
    </footer>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/js/admin/admin.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
