@model IEnumerable<TeamWorkFlow.Core.Models.Admin.UserRole.DemotionRequestViewModel>
@{
    ViewData["Title"] = "Admin Demotion Requests";
    var pendingCount = ViewBag.PendingCount as int? ?? 0;
}

@section Styles {
    <link rel="stylesheet" href="~/css/admin/admin.css" asp-append-version="true" />
    <style>
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }
        .status-approved {
            background-color: #d1fae5;
            color: #065f46;
        }
        .status-rejected {
            background-color: #fee2e2;
            color: #991b1b;
        }
        .status-cancelled {
            background-color: #f3f4f6;
            color: #6b7280;
        }
        .status-expired {
            background-color: #fde2e7;
            color: #be185d;
        }
        .action-btn {
            padding: 0.375rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
            margin-right: 0.5rem;
        }
        .btn-approve {
            background-color: #10b981;
            color: white;
        }
        .btn-approve:hover {
            background-color: #059669;
            color: white;
        }
        .btn-reject {
            background-color: #ef4444;
            color: white;
        }
        .btn-reject:hover {
            background-color: #dc2626;
            color: white;
        }
        .btn-details {
            background-color: #6366f1;
            color: white;
        }
        .btn-details:hover {
            background-color: #4f46e5;
            color: white;
        }
        .btn-cancel {
            background-color: #f59e0b;
            color: white;
        }
        .btn-cancel:hover {
            background-color: #d97706;
            color: white;
        }
        .urgent-indicator {
            background-color: #ef4444;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #1f2937;
        }
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }
    </style>
}

<div class="admin-container">
    <!-- Admin Header -->
    <div class="admin-header">
        <h1 class="admin-title">Admin Demotion Requests</h1>
        <p class="admin-subtitle">Review and manage administrator demotion requests</p>
        <div style="display: flex; gap: 1rem; align-items: center; margin-top: 1rem;">
            <span class="admin-badge">Administrator Access</span>
            <a asp-action="Index" class="action-btn" style="background-color: #6b7280; color: white; text-decoration: none;">
                ← Back to User Management
            </a>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">@Model.Count()</div>
            <div class="stat-label">Total Requests</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">@pendingCount</div>
            <div class="stat-label">Pending Approval</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">@Model.Count(r => r.Status == TeamWorkFlow.Infrastructure.Data.Models.DemotionRequestStatus.Approved)</div>
            <div class="stat-label">Approved</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">@Model.Count(r => r.Status == TeamWorkFlow.Infrastructure.Data.Models.DemotionRequestStatus.Rejected)</div>
            <div class="stat-label">Rejected</div>
        </div>
    </div>

    <!-- Requests Table -->
    <div class="dashboard-card">
        <div class="dashboard-card-header">
            <h3 class="dashboard-card-title">Demotion Requests</h3>
            <p class="dashboard-card-description">All administrator demotion requests and their current status</p>
        </div>

        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Target Admin</th>
                            <th>Requested By</th>
                            <th>Reason</th>
                            <th>Status</th>
                            <th>Requested</th>
                            <th>Expires</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var request in Model.OrderByDescending(r => r.RequestedAt))
                        {
                            <tr>
                                <td>
                                    <strong>@request.TargetUserFullName</strong><br>
                                    <small style="color: #6b7280;">@request.TargetUserEmail</small>
                                </td>
                                <td>
                                    <strong>@request.RequestedByUserFullName</strong><br>
                                    <small style="color: #6b7280;">@request.RequestedByUserEmail</small>
                                </td>
                                <td>
                                    @if (request.Reason?.Length > 50)
                                    {
                                        <span title="@request.Reason">@(request.Reason.Substring(0, 50))...</span>
                                    }
                                    else
                                    {
                                        @request.Reason
                                    }
                                </td>
                                <td>
                                    <span class="status-badge <EMAIL>().ToLower()">
                                        @request.StatusText
                                    </span>
                                    @if (request.IsPendingAndValid && request.TimeUntilExpiration.TotalHours < 24)
                                    {
                                        <br><span class="urgent-indicator">Expires Soon</span>
                                    }
                                </td>
                                <td>@request.RequestedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                <td>
                                    @if (request.IsPendingAndValid)
                                    {
                                        <span>@request.TimeUntilExpirationText</span>
                                    }
                                    else if (request.IsExpired)
                                    {
                                        <span style="color: #ef4444;">Expired</span>
                                    }
                                    else
                                    {
                                        <span>@request.ProcessedAt?.ToString("dd/MM/yyyy HH:mm")</span>
                                    }
                                </td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <a asp-action="DemotionRequestDetails" asp-route-id="@request.Id" class="action-btn btn-details">
                                            Details
                                        </a>
                                        
                                        @if (request.IsPendingAndValid && request.CanApprove)
                                        {
                                            <form asp-action="ApproveDemotionRequest" method="post" style="display: inline;">
                                                @Html.AntiForgeryToken()
                                                <input type="hidden" name="id" value="@request.Id" />
                                                <button type="submit" class="action-btn btn-approve" 
                                                        onclick="return confirm('Are you sure you want to APPROVE this demotion request? This will immediately demote @request.TargetUserFullName from Administrator to Operator.')">
                                                    Approve
                                                </button>
                                            </form>
                                            
                                            <form asp-action="RejectDemotionRequest" method="post" style="display: inline;">
                                                @Html.AntiForgeryToken()
                                                <input type="hidden" name="id" value="@request.Id" />
                                                <button type="submit" class="action-btn btn-reject" 
                                                        onclick="return confirm('Are you sure you want to REJECT this demotion request?')">
                                                    Reject
                                                </button>
                                            </form>
                                        }
                                        
                                        @if (request.IsPendingAndValid && request.CanCancel)
                                        {
                                            <form asp-action="CancelDemotionRequest" method="post" style="display: inline;">
                                                @Html.AntiForgeryToken()
                                                <input type="hidden" name="id" value="@request.Id" />
                                                <button type="submit" class="action-btn btn-cancel" 
                                                        onclick="return confirm('Are you sure you want to cancel this demotion request?')">
                                                    Cancel
                                                </button>
                                            </form>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div style="text-align: center; padding: 3rem; color: #6b7280;">
                <p>No demotion requests found.</p>
                <a asp-action="Index" class="action-btn" style="background-color: #6366f1; color: white; text-decoration: none;">
                    Go to User Management
                </a>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script src="~/js/admin/admin.js" asp-append-version="true"></script>
}
