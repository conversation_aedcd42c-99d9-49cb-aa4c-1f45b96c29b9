@model TeamWorkFlow.Core.Models.Admin.UserRole.CreateDemotionRequestViewModel
@{
    ViewData["Title"] = "Request Admin Demotion";
}

@section Styles {
    <link rel="stylesheet" href="~/css/admin/admin.css" asp-append-version="true" />
    <style>
        .form-container {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-bottom: 1.5rem;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        .form-label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            font-size: 0.875rem;
        }
        .form-control:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .warning-box {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 0.375rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .warning-text {
            color: #92400e;
            font-size: 0.875rem;
        }
        .info-box {
            background-color: #dbeafe;
            border: 1px solid #3b82f6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .info-text {
            color: #1e40af;
            font-size: 0.875rem;
        }
        .action-btn {
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
            margin-right: 0.75rem;
        }
        .btn-submit {
            background-color: #f59e0b;
            color: white;
        }
        .btn-submit:hover {
            background-color: #d97706;
        }
        .btn-cancel {
            background-color: #6b7280;
            color: white;
        }
        .btn-cancel:hover {
            background-color: #4b5563;
            color: white;
        }
        .checkbox-container {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
        }
        .checkbox-container input[type="checkbox"] {
            margin-top: 0.25rem;
        }
    </style>
}

<div class="admin-container">
    <!-- Admin Header -->
    <div class="admin-header">
        <h1 class="admin-title">Request Admin Demotion</h1>
        <p class="admin-subtitle">Create a request to demote an administrator (requires approval from another admin)</p>
        <span class="admin-badge">Administrator Access</span>
    </div>

    <!-- Warning Box -->
    <div class="warning-box">
        <p class="warning-text">
            <strong>Important:</strong> This action requires approval from another administrator. 
            The demotion will not take effect until another admin reviews and approves your request.
        </p>
    </div>

    <!-- Form Container -->
    <div class="form-container">
        <h3 style="margin-bottom: 1.5rem; color: #1f2937;">Demotion Request Details</h3>
        
        <form asp-area="Admin" asp-controller="UserRole" asp-action="RequestDemotion" method="post">
            @Html.AntiForgeryToken()
            <input type="hidden" asp-for="TargetUserId" />
            
            <!-- Target User Information -->
            <div class="form-group">
                <label class="form-label">Administrator to Demote:</label>
                <div style="background-color: #f9fafb; padding: 1rem; border-radius: 0.375rem; border: 1px solid #e5e7eb;">
                    <strong>@Model.TargetUserFullName</strong><br>
                    <span style="color: #6b7280;">@Model.TargetUserEmail</span>
                </div>
            </div>

            <!-- Reason -->
            <div class="form-group">
                <label asp-for="Reason" class="form-label"></label>
                <textarea asp-for="Reason" class="form-control" rows="4" 
                         placeholder="Please provide a detailed reason for this demotion request. This will be visible to other administrators who review the request."></textarea>
                <span asp-validation-for="Reason" class="text-danger"></span>
            </div>

            <!-- Information Box -->
            <div class="info-box">
                <p class="info-text">
                    <strong>Review Process:</strong><br>
                    • Your request will be sent to all other administrators for review<br>
                    • Any administrator (except you) can approve or reject the request<br>
                    • The request will expire automatically after 7 days if not processed<br>
                    • You can cancel your request at any time before it's processed
                </p>
            </div>

            <!-- Acknowledgment -->
            <div class="form-group">
                <div class="checkbox-container">
                    @Html.CheckBoxFor(m => m.AcknowledgeApprovalRequired, new { @class = "form-check-input" })
                    <label asp-for="AcknowledgeApprovalRequired" class="form-label" style="margin-bottom: 0;">
                        I understand that this demotion request requires approval from another administrator
                    </label>
                </div>
                <span asp-validation-for="AcknowledgeApprovalRequired" class="text-danger"></span>
            </div>

            <!-- Action Buttons -->
            <div style="margin-top: 2rem;">
                <button type="submit" class="action-btn btn-submit">
                    Submit Demotion Request
                </button>
                <a asp-action="Index" class="action-btn btn-cancel">
                    Cancel
                </a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    <script src="~/js/admin/admin.js" asp-append-version="true"></script>
    @* Temporarily disabled validation scripts to test form submission *@
    @* @{await Html.RenderPartialAsync("_ValidationScriptsPartial");} *@
}
