﻿@page
@model LoginModel

@{
    ViewData["Title"] = "Welcome Back";
}

@section Styles {
    <link rel="stylesheet" href="~/css/auth-pages.css" asp-append-version="true" />
}

<div class="auth-card">
    <div class="auth-header">
        <h1 class="auth-title">Welcome Back</h1>
        <p class="auth-subtitle">Sign in to your TeamWorkFlow account</p>
    </div>

    <form id="account" method="post" class="auth-form">
        <div asp-validation-summary="ModelOnly" class="text-danger"></div>

        <div class="form-floating">
            <input asp-for="Input.Email" class="form-control" autocomplete="username" aria-required="true" placeholder="Enter your email" />
            <label asp-for="Input.Email" class="form-label">Email Address</label>
            <span asp-validation-for="Input.Email" class="text-danger"></span>
        </div>

        <div class="form-floating">
            <input asp-for="Input.Password" class="form-control" autocomplete="current-password" aria-required="true" placeholder="Enter your password" />
            <label asp-for="Input.Password" class="form-label">Password</label>
            <span asp-validation-for="Input.Password" class="text-danger"></span>
        </div>

        <div class="auth-checkbox">
            <input class="form-check-input" asp-for="Input.RememberMe" />
            <label asp-for="Input.RememberMe" class="form-label">
                @Html.DisplayNameFor(m => m.Input.RememberMe)
            </label>
        </div>

        <button id="login-submit" type="submit" class="auth-submit-btn">
            <svg width="16" height="16" fill="currentColor" class="me-2" viewBox="0 0 16 16">
                <path fill-rule="evenodd" d="M6 3.5a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-2a.5.5 0 0 0-1 0v2A1.5 1.5 0 0 0 6.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 14.5 2h-8A1.5 1.5 0 0 0 5 3.5v2a.5.5 0 0 0 1 0v-2z"/>
                <path fill-rule="evenodd" d="M11.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 0 0-.708.708L10.293 7.5H1.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3z"/>
            </svg>
            Sign In
        </button>

        <div class="auth-links">
            <p class="mb-0">
                Don't have an account?
                <a asp-page="./Register" class="auth-link" asp-route-returnUrl="@Model.ReturnUrl">
                    Create one here
                    <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"/>
                    </svg>
                </a>
            </p>
        </div>
    </form>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}


