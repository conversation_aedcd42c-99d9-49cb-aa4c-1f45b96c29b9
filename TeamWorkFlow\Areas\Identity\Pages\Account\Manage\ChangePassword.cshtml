﻿@page
@model ChangePasswordModel
@{
    ViewData["Title"] = "Password Settings";
    ViewData["ActivePage"] = ManageNavPages.ChangePassword;
}

<div class="profile-page">
    <div class="profile-container">
        <!-- Profile Header -->
        <partial name="_ProfileHeader" />

        <div class="profile-content slide-in">
    <div class="profile-content-header">
        <h2 class="profile-content-title">
            <i class="fas fa-key me-2"></i>
            Password Security
        </h2>
        <p class="profile-content-subtitle">Update your password to keep your account secure</p>
    </div>

    <!-- Status Message -->
    @if (!string.IsNullOrEmpty(Model.StatusMessage))
    {
        <div class="status-message @(Model.StatusMessage.Contains("changed") || Model.StatusMessage.Contains("updated") ? "success" : "error")">
            <i class="fas @(Model.StatusMessage.Contains("changed") || Model.StatusMessage.Contains("updated") ? "fa-check-circle" : "fa-exclamation-triangle") me-2"></i>
            @Model.StatusMessage
        </div>
    }

    <div class="row">
        <div class="col-lg-8">
            <form id="change-password-form" method="post" class="profile-form">
                <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                <div class="form-floating">
                    <input asp-for="Input.OldPassword" class="form-control" autocomplete="current-password" aria-required="true" placeholder="Enter current password" />
                    <label asp-for="Input.OldPassword" class="form-label">
                        <i class="fas fa-lock me-2"></i>Current Password
                    </label>
                    <span asp-validation-for="Input.OldPassword" class="text-danger"></span>
                </div>

                <div class="form-floating">
                    <input asp-for="Input.NewPassword" class="form-control" autocomplete="new-password" aria-required="true" placeholder="Enter new password" id="new-password" />
                    <label asp-for="Input.NewPassword" class="form-label">
                        <i class="fas fa-key me-2"></i>New Password
                    </label>
                    <span asp-validation-for="Input.NewPassword" class="text-danger"></span>
                </div>

                <!-- Password Strength Indicator -->
                <div id="password-strength" class="password-strength mb-3" style="display: none;">
                    <div class="password-strength-bar">
                        <div class="password-strength-fill"></div>
                    </div>
                    <div class="password-strength-text">Password strength: <span id="strength-text">Weak</span></div>
                </div>

                <div class="form-floating">
                    <input asp-for="Input.ConfirmPassword" class="form-control" autocomplete="new-password" aria-required="true" placeholder="Confirm new password" id="confirm-password" />
                    <label asp-for="Input.ConfirmPassword" class="form-label">
                        <i class="fas fa-check me-2"></i>Confirm New Password
                    </label>
                    <span asp-validation-for="Input.ConfirmPassword" class="text-danger"></span>
                </div>

                <!-- Password Match Indicator -->
                <div id="password-match" class="password-match mb-3" style="display: none;">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>Passwords match</span>
                </div>

                <button type="submit" class="profile-btn profile-btn-primary" id="update-password-btn">
                    <i class="fas fa-save me-2"></i>
                    Update Password
                </button>
            </form>
        </div>

        <div class="col-lg-4">
            <div class="security-indicator strong">
                <div class="security-indicator-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div>
                    <strong>Password Security</strong>
                    <p class="mb-0 text-muted">Strong passwords protect your account from unauthorized access</p>
                </div>
            </div>

            <div class="mt-3 p-3 bg-light rounded">
                <h6><i class="fas fa-lightbulb me-2"></i>Password Requirements</h6>
                <ul class="mb-0 small text-muted">
                    <li>At least 8 characters long</li>
                    <li>Include uppercase and lowercase letters</li>
                    <li>Include at least one number</li>
                    <li>Include at least one special character</li>
                    <li>Avoid common passwords</li>
                </ul>
            </div>

            <div class="mt-3 p-3 bg-info bg-opacity-10 rounded">
                <h6><i class="fas fa-info-circle me-2"></i>Security Tips</h6>
                <ul class="mb-0 small text-muted">
                    <li>Use a unique password for this account</li>
                    <li>Consider using a password manager</li>
                    <li>Change your password regularly</li>
                    <li>Never share your password</li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const newPasswordInput = document.getElementById('new-password');
            const confirmPasswordInput = document.getElementById('confirm-password');
            const strengthIndicator = document.getElementById('password-strength');
            const strengthFill = document.querySelector('.password-strength-fill');
            const strengthText = document.getElementById('strength-text');
            const matchIndicator = document.getElementById('password-match');
            const updateBtn = document.getElementById('update-password-btn');

            // Password strength checker
            function checkPasswordStrength(password) {
                let strength = 0;
                let strengthLabel = 'Very Weak';
                let color = '#ef4444';

                if (password.length >= 8) strength++;
                if (/[a-z]/.test(password)) strength++;
                if (/[A-Z]/.test(password)) strength++;
                if (/[0-9]/.test(password)) strength++;
                if (/[^A-Za-z0-9]/.test(password)) strength++;

                switch(strength) {
                    case 0:
                    case 1:
                        strengthLabel = 'Very Weak';
                        color = '#ef4444';
                        break;
                    case 2:
                        strengthLabel = 'Weak';
                        color = '#f59e0b';
                        break;
                    case 3:
                        strengthLabel = 'Fair';
                        color = '#eab308';
                        break;
                    case 4:
                        strengthLabel = 'Good';
                        color = '#22c55e';
                        break;
                    case 5:
                        strengthLabel = 'Strong';
                        color = '#10b981';
                        break;
                }

                return { strength, strengthLabel, color };
            }

            // Update password strength indicator
            newPasswordInput.addEventListener('input', function() {
                const password = this.value;

                if (password.length > 0) {
                    strengthIndicator.style.display = 'block';
                    const result = checkPasswordStrength(password);

                    strengthFill.style.width = (result.strength * 20) + '%';
                    strengthFill.style.backgroundColor = result.color;
                    strengthText.textContent = result.strengthLabel;
                    strengthText.style.color = result.color;
                } else {
                    strengthIndicator.style.display = 'none';
                }

                checkPasswordMatch();
            });

            // Check password match
            function checkPasswordMatch() {
                const newPassword = newPasswordInput.value;
                const confirmPassword = confirmPasswordInput.value;

                if (confirmPassword.length > 0) {
                    if (newPassword === confirmPassword) {
                        matchIndicator.style.display = 'block';
                        confirmPasswordInput.classList.remove('is-invalid');
                        confirmPasswordInput.classList.add('is-valid');
                    } else {
                        matchIndicator.style.display = 'none';
                        confirmPasswordInput.classList.remove('is-valid');
                        confirmPasswordInput.classList.add('is-invalid');
                    }
                } else {
                    matchIndicator.style.display = 'none';
                    confirmPasswordInput.classList.remove('is-valid', 'is-invalid');
                }
            }

            confirmPasswordInput.addEventListener('input', checkPasswordMatch);

            // Form submission
            document.getElementById('change-password-form').addEventListener('submit', function() {
                updateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
                updateBtn.disabled = true;
            });
        });
    </script>

    <style>
        .password-strength {
            margin-top: 0.5rem;
        }

        .password-strength-bar {
            width: 100%;
            height: 8px;
            background-color: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .password-strength-fill {
            height: 100%;
            width: 0%;
            transition: all 0.3s ease;
            border-radius: 4px;
        }

        .password-strength-text {
            font-size: 0.875rem;
            font-weight: 500;
        }

        .password-match {
            color: #10b981;
            font-size: 0.875rem;
            font-weight: 500;
        }
    </style>
}
