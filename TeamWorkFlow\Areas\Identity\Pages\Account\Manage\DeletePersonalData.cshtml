﻿@page
@model DeletePersonalDataModel
@{
    ViewData["Title"] = "Delete Personal Data";
    ViewData["ActivePage"] = ManageNavPages.PersonalData;
}

<h3>@ViewData["Title"]</h3>

<div class="alert alert-warning" role="alert">
    <p>
        <strong>Deleting this data will permanently remove your account, and this cannot be recovered.</strong>
    </p>
</div>

<div>
    <form id="delete-user" method="post">
        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
        @if (Model.RequirePassword)
        {
            <div class="form-floating">
                <input asp-for="Input.Password" class="form-control" autocomplete="current-password" aria-required="true" />
                <label asp-for="Input.Password" class="form-label"></label>
                <span asp-validation-for="Input.Password" class="text-danger"></span>
            </div>
        }
        <button class="w-100 btn btn-lg btn-danger" type="submit">Delete data and close my account</button>
    </form>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
