﻿@page
@model EmailModel
@{
    ViewData["Title"] = "Email Settings";
    ViewData["ActivePage"] = ManageNavPages.Email;
}

<div class="profile-page">
    <div class="profile-container">
        <!-- Profile Header -->
        <partial name="_ProfileHeader" />

        <div class="profile-content slide-in">
    <div class="profile-content-header">
        <h2 class="profile-content-title">
            <i class="fas fa-envelope me-2"></i>
            Email Management
        </h2>
        <p class="profile-content-subtitle">Manage your email address and verification status</p>
    </div>

    <!-- Status Message -->
    @if (!string.IsNullOrEmpty(Model.StatusMessage))
    {
        <div class="status-message @(Model.StatusMessage.Contains("sent") || Model.StatusMessage.Contains("changed") ? "success" : "info")">
            <i class="fas @(Model.StatusMessage.Contains("sent") || Model.StatusMessage.Contains("changed") ? "fa-check-circle" : "fa-info-circle") me-2"></i>
            @Model.StatusMessage
        </div>
    }

    <form id="email-form" method="post" class="profile-form">
        <div asp-validation-summary="All" class="text-danger mb-3"></div>

        <!-- Current Email Section -->
        <div class="mb-4">
            <h4 class="h5 mb-3">
                <i class="fas fa-at me-2"></i>
                Current Email Address
            </h4>

            <div class="form-floating">
                <input asp-for="Email" class="form-control" disabled />
                <label asp-for="Email" class="form-label">Current Email</label>
            </div>
        </div>

        <!-- Change Email Section -->
        <div class="mb-4">
            <h4 class="h5 mb-3">
                <i class="fas fa-edit me-2"></i>
                Change Email Address
            </h4>

            <div class="form-floating">
                <input asp-for="Input.NewEmail" class="form-control" autocomplete="email" aria-required="true" placeholder="Enter new email address" />
                <label asp-for="Input.NewEmail" class="form-label">
                    <i class="fas fa-envelope me-2"></i>New Email Address
                </label>
                <span asp-validation-for="Input.NewEmail" class="text-danger"></span>
            </div>

            <button id="change-email-button" type="submit" asp-page-handler="ChangeEmail" class="profile-btn profile-btn-primary mt-3">
                <i class="fas fa-save me-2"></i>
                Change Email
            </button>
        </div>
    </form>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const changeEmailBtn = document.getElementById('change-email-button');

            if (changeEmailBtn) {
                changeEmailBtn.addEventListener('click', function() {
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Changing...';
                    this.disabled = true;
                });
            }
        });
    </script>
}
