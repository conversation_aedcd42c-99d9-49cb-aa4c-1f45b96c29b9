﻿@page
@model SetPasswordModel
@{
    ViewData["Title"] = "Set password";
    ViewData["ActivePage"] = ManageNavPages.ChangePassword;
}

<h3>Set your password</h3>
<partial name="_StatusMessage" for="StatusMessage" />
<p class="text-info">
    You do not have a local username/password for this site. Add a local
    account so you can log in without an external login.
</p>
<div class="row">
    <div class="col-md-6">
        <form id="set-password-form" method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-floating">
                <input asp-for="Input.NewPassword" class="form-control" autocomplete="new-password" />
                <label asp-for="Input.NewPassword" class="form-label"></label>
                <span asp-validation-for="Input.NewPassword" class="text-danger"></span>
            </div>
            <div class="form-floating">
                <input asp-for="Input.ConfirmPassword" class="form-control" autocomplete="new-password" />
                <label asp-for="Input.ConfirmPassword" class="form-label"></label>
                <span asp-validation-for="Input.ConfirmPassword" class="text-danger"></span>
            </div>
            <button type="submit" class="w-100 btn btn-lg btn-primary">Set password</button>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
