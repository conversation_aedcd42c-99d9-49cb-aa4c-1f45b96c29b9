﻿@{
    if (ViewData.TryGetValue("ParentLayout", out var parentLayout) && parentLayout !=  null)
    {
        Layout = parentLayout.ToString();
    }
    else
    {
        Layout = "/Views/Shared/_Layout.cshtml";
    }
}

@section Styles {
    <link rel="stylesheet" href="~/css/profile-pages.css" asp-append-version="true" />
    @RenderSection("Styles", required: false)
}

<div class="profile-page">
    <div class="profile-container">
        <div class="row g-4">
            <div class="col-lg-3">
                <partial name="_ManageNav" />
            </div>
            <div class="col-lg-9">
                @RenderBody()
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @RenderSection("Scripts", required: false)
}
