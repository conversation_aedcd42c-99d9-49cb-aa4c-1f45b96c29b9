﻿@inject SignInManager<IdentityUser> SignInManager
@{
    var hasExternalLogins = (await SignInManager.GetExternalAuthenticationSchemesAsync()).Any();
}

<!-- Profile Account Settings Navigation -->
<div class="profile-nav">
    <button class="profile-nav-toggle" onclick="toggleProfileNav()">
        <i class="fas fa-bars me-2"></i>
        Account Settings
        <i class="fas fa-chevron-down ms-auto"></i>
    </button>

    <ul class="nav nav-pills flex-column">
        <li class="nav-item">
            <a class="nav-link @ManageNavPages.IndexNavClass(ViewContext)" id="profile" asp-page="./Index">
                <i class="fas fa-user me-2"></i>
                Profile
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link @ManageNavPages.EmailNavClass(ViewContext)" id="email" asp-page="./Email">
                <i class="fas fa-envelope me-2"></i>
                Email
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link @ManageNavPages.ChangePasswordNavClass(ViewContext)" id="change-password" asp-page="./ChangePassword">
                <i class="fas fa-key me-2"></i>
                Password
            </a>
        </li>
        @if (hasExternalLogins)
        {
            <li id="external-logins" class="nav-item">
                <a id="external-login" class="nav-link @ManageNavPages.ExternalLoginsNavClass(ViewContext)" asp-page="./ExternalLogins">
                    <i class="fas fa-link me-2"></i>
                    External logins
                </a>
            </li>
        }

        <li class="nav-item">
            <a class="nav-link @ManageNavPages.PersonalDataNavClass(ViewContext)" id="personal-data" asp-page="./PersonalData">
                <i class="fas fa-database me-2"></i>
                Personal data
            </a>
        </li>
    </ul>
</div>

<script>
function toggleProfileNav() {
    const nav = document.querySelector('.profile-nav');
    const toggle = document.querySelector('.profile-nav-toggle');
    const icon = toggle.querySelector('.fa-chevron-down');

    nav.classList.toggle('open');

    if (nav.classList.contains('open')) {
        icon.style.transform = 'rotate(180deg)';
    } else {
        icon.style.transform = 'rotate(0deg)';
    }
}
</script>
