﻿@page
@model RegisterModel
@{
    ViewData["Title"] = "Join <PERSON>Work<PERSON>low";
}

@section Styles {
    <link rel="stylesheet" href="~/css/auth-pages.css" asp-append-version="true" />
}

<div class="auth-card">
    <div class="auth-header">
        <h1 class="auth-title">Join TeamWorkFlow</h1>
        <p class="auth-subtitle">Create your account and start streamlining your workflow</p>
    </div>

    <form id="registerForm" asp-route-returnUrl="@Model.ReturnUrl" method="post" class="auth-form">
        <div asp-validation-summary="ModelOnly" class="text-danger"></div>

        <div class="form-floating">
            <input asp-for="Input.Email" class="form-control" autocomplete="username" aria-required="true" placeholder="Enter your email" />
            <label asp-for="Input.Email">Email Address</label>
            <span asp-validation-for="Input.Email" class="text-danger"></span>
        </div>

        <div class="form-floating">
            <input asp-for="Input.Password" class="form-control" autocomplete="new-password" aria-required="true" placeholder="Create a password" />
            <label asp-for="Input.Password">Password</label>
            <span asp-validation-for="Input.Password" class="text-danger"></span>
        </div>

        <div class="form-floating">
            <input asp-for="Input.ConfirmPassword" class="form-control" autocomplete="new-password" aria-required="true" placeholder="Confirm your password" />
            <label asp-for="Input.ConfirmPassword">Confirm Password</label>
            <span asp-validation-for="Input.ConfirmPassword" class="text-danger"></span>
        </div>

        <button id="registerSubmit" type="submit" class="auth-submit-btn">
            <svg width="16" height="16" fill="currentColor" class="me-2" viewBox="0 0 16 16">
                <path d="M15 14s1 0 1-1-1-4-5-4-5 3-5 4 1 1 1 1h8Zm-7.978-1A.261.261 0 0 1 7 12.996c.001-.264.167-1.03.76-1.72C8.312 10.629 9.282 10 11 10c1.717 0 2.687.63 3.24 1.276.593.69.758 1.457.76 1.72l-.008.002A.274.274 0 0 1 15 13H7.022ZM11 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4Zm3-2a3 3 0 1 1-6 0 3 3 0 0 1 6 0ZM6.936 9.28a5.88 5.88 0 0 0-1.23-.247A7.35 7.35 0 0 0 5 9c-4 0-5 3-5 4 0 .667.333 1 1 1h4.216A2.238 2.238 0 0 1 5 13c0-1.01.377-2.042 1.09-2.904.243-.294.526-.569.846-.816ZM4.92 10A5.493 5.493 0 0 0 4 13H1c0-.26.164-1.03.76-1.724.545-.636 1.492-1.256 3.16-1.275ZM1.5 5.5a3 3 0 1 1 6 0 3 3 0 0 1-6 0Zm3-2a2 2 0 1 0 0 4 2 2 0 0 0 0-4Z"/>
            </svg>
            Create Account
        </button>

        <div class="auth-links">
            <p class="mb-0">
                Already have an account?
                <a asp-page="./Login" class="auth-link" asp-route-returnUrl="@Model.ReturnUrl">
                    Sign in here
                    <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"/>
                    </svg>
                </a>
            </p>
        </div>
    </form>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}


