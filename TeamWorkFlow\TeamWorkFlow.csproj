<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-TeamWorkFlow-ad7f45d7-d658-4d4f-bfeb-8af8ccb39f7b</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="6.0.27" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="6.0.27" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="6.0.27" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="6.0.27" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="6.0.27" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.27" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.27">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="6.0.16" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TeamWorkFlow.Core\TeamWorkFlow.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Areas\Admin\Data\" />
    <Folder Include="Areas\Admin\Models\" />
  </ItemGroup>

</Project>
