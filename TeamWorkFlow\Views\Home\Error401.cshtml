﻿@{
	ViewData["Title"] = "Unauthorized";
}

<div class="row">
	<div class="col-3"></div>
	<div class="col-6">
		<br>
		<div class="row">
			<div class="col-10">
				<img src="https://www.elegantthemes.com/blog/wp-content/uploads/2019/12/401-error-wordpress-featured-image.jpg" alt="Alternative image" class="img-thumbnail">
				<div class="text-left h4">
					<p>Sorry. You don't have access to this page!</p>
				</div>
				<div class="text-left h4">  Go back to <a asp-controller="Home" asp-action="Index">Home</a> Page</div>
			</div>
		</div>
	</div>
	<div class="col-3"></div>
</div>