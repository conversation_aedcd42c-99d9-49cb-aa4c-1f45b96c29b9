﻿@model AllMachinesQueryModel
@{
	ViewData["Title"] = "CMMs - Coordinate Measuring Machines";
}

@{
	var previousPage = Model.CurrentPage - 1;
	if (previousPage < 1)
	{
		previousPage = 1;
	}

	var maxPage = Math.Ceiling((decimal)Model.TotalMachinesCount / (decimal)Model.MachinesPerPage);
}

@section Styles {
	<link rel="stylesheet" href="~/css/pages/machines.css" asp-append-version="true" />
}

<div class="machines-container">
	<!-- Header Section -->
	<div class="machines-header">
		<h1 class="machines-title">@ViewData["Title"]</h1>
		<p class="machines-subtitle">Manage and monitor your precision measuring equipment</p>
	</div>

	@if (User?.Identity?.IsAuthenticated ?? false)
	{
		@if (User.IsAdmin())
		{
			<div class="mb-6">
				<a class="btn-success" asp-area="" asp-controller="Machine" asp-action="Add">
					<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
					</svg>
					Add New Machine
				</a>
			</div>
		}

		<!-- Search and Filter Section -->
		<div class="search-section">
			<form method="get" id="search-form" class="search-form">
				<div class="form-group">
					<label asp-for="Search" class="form-label">Search Machines</label>
					<input asp-for="Search" id="search-input" class="form-input" placeholder="Search by machine name...">
				</div>

				<div class="form-group">
					<label asp-for="Sorting" class="form-label">Sort By</label>
					<select asp-for="Sorting" id="sorting-select" class="form-select">
						<option value="0">Last Added</option>
						<option value="1">Name (A-Z)</option>
						<option value="2">Name (Z-A)</option>
						<option value="3">Calibration Date (Earliest)</option>
						<option value="4">Calibration Date (Latest)</option>
						<option value="5">Capacity (Low to High)</option>
						<option value="6">Capacity (High to Low)</option>
					</select>
				</div>

				<div class="search-buttons">
					<input type="submit" value="Search" class="btn-primary" />
					<a asp-action="All" id="clear-search" class="btn-secondary">Clear</a>
				</div>
			</form>
		</div>

		<!-- Machines Grid -->
		<div class="machines-grid">
			@foreach (var m in Model.Machines)
			{
				<div class="machine-card fade-in">
					<!-- Card Image -->
					<div class="machine-card-image-container">
						@{
							string machineImageUrl = "/img/machines/default-cmm.svg";
							string machineBrand = "default";

							if (m.Name.ToLower().Contains("zeiss"))
							{
								machineBrand = "zeiss";
								machineImageUrl = "/img/machines/zeiss-cmm.svg";
							}
							else if (m.Name.ToLower().Contains("mitutoyo"))
							{
								machineBrand = "mitutoyo";
								machineImageUrl = "/img/machines/mitutoyo-cmm.svg";
							}
							else if (m.Name.ToLower().Contains("hexagon"))
							{
								machineBrand = "hexagon";
								machineImageUrl = "/img/machines/hexagon-cmm.svg";
							}
							else if (m.Name.ToLower().Contains("brown"))
							{
								machineBrand = "brown";
								machineImageUrl = "/img/machines/brown-cmm.svg";
							}
							else if (m.Name.ToLower().Contains("coord3"))
							{
								machineBrand = "coord3";
								machineImageUrl = "/img/machines/coord3-cmm.svg";
							}
						}
						<div class="machine-image-placeholder" data-brand="@machineBrand">
							<img src="@machineImageUrl" alt="@m.Name" class="machine-card-image"
								 onerror="this.style.display='none'; this.parentElement.classList.add('image-error');" />
							<div class="machine-image-fallback">
								<div class="machine-icon">
									<svg viewBox="0 0 24 24" fill="currentColor">
										<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
									</svg>
								</div>
								<div class="machine-brand">@machineBrand.ToUpper()</div>
								<div class="machine-type">CMM</div>
							</div>
						</div>
						<div class="machine-card-image-overlay"></div>
					</div>

					<!-- Card Content -->
					<div class="machine-card-content">
						<!-- Card Header -->
						<div class="machine-card-header">
							<h3 class="machine-title">@m.Name</h3>
							<span class="machine-status @(m.IsCalibrated ? "machine-status-calibrated" : "machine-status-not-calibrated")">
								@(m.IsCalibrated ? "Calibrated" : "Not Calibrated")
							</span>
						</div>

						<!-- Machine Details -->
						<div class="machine-details">
							<div class="machine-detail-item">
								<span class="machine-detail-label">
									<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
									</svg>
									Next Calibration
								</span>
								<span class="machine-detail-value">@m.CalibrationSchedule</span>
							</div>
							<div class="machine-detail-item">
								<span class="machine-detail-label">
									<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
									</svg>
									Daily Capacity
								</span>
								<span class="machine-detail-value">
									<span class="capacity-indicator">@m.Capacity hours/day</span>
								</span>
							</div>
							<div class="machine-detail-item">
								<span class="machine-detail-label">
									<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
									</svg>
									Status
								</span>
								<span class="machine-detail-value">@(m.IsCalibrated ? "Ready" : "Needs Calibration")</span>
							</div>
						</div>

						<!-- Assignment Status -->
						@if (m.IsOccupied)
						{
							<div class="machine-assignment-info occupied">
								<div class="assignment-header">
									<svg class="assignment-icon occupied" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
									</svg>
									<span class="assignment-status">Currently Occupied</span>
								</div>
								<div class="assignment-details">
									<div class="assignment-task">
										<strong>Task:</strong> @m.AssignedTaskName
									</div>
									<div class="assignment-project">
										<strong>Project:</strong> #@m.AssignedTaskProjectNumber
									</div>
									@if (!string.IsNullOrEmpty(m.AssignedOperatorNames))
									{
										<div class="assignment-operators">
											<strong>Operators:</strong> @m.AssignedOperatorNames
										</div>
									}
									@if (!string.IsNullOrEmpty(m.TaskStatus))
									{
										<div class="assignment-task-status">
											<strong>Status:</strong> <span class="task-status-badge <EMAIL>().Replace(" ", "-")">@m.TaskStatus</span>
										</div>
									}
								</div>
							</div>
						}
						else
						{
							<div class="machine-assignment-info available">
								<div class="assignment-header">
									<svg class="assignment-icon available" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
									</svg>
									<span class="assignment-status">Available for Assignment</span>
								</div>
								@if (m.IsCalibrated)
								{
									<div class="assignment-details">
										<div class="availability-message">
											This machine is ready to be assigned to a new task.
										</div>
									</div>
								}
								else
								{
									<div class="assignment-details">
										<div class="availability-message warning">
											Machine requires calibration before it can be assigned to tasks.
										</div>
									</div>
								}
							</div>
						}

						<!-- Calibration Status Alert -->
						@if (!m.IsCalibrated)
						{
							<div class="calibration-warning">
								Machine requires calibration before use
							</div>
						}
						else
						{
							<div class="calibration-ok">
								Machine is calibrated and ready for use
							</div>
						}

						<!-- Action Buttons -->
						<div class="machine-actions">
							@if (User.IsAdmin())
							{
								<a asp-controller="Machine" asp-action="Details" asp-route-id="@m.Id"
								   asp-route-extension="@m.GetMachineExtension()" class="action-btn action-btn-details">
									<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
									</svg>
									Details
								</a>
								<a asp-controller="Machine" asp-action="Edit" asp-route-id="@m.Id"
								   asp-route-extension="@m.GetMachineExtension()" class="action-btn action-btn-edit">
									<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
									</svg>
									Edit
								</a>
								<a asp-controller="Machine" asp-action="Delete" asp-route-id="@m.Id"
								   asp-route-extension="@m.GetMachineExtension()" class="action-btn action-btn-delete">
									<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
									</svg>
									Delete
								</a>
							}
							else if (User.IsOperator())
							{
								<a asp-controller="Machine" asp-action="Details" asp-route-id="@m.Id"
								   asp-route-extension="@m.GetMachineExtension()" class="action-btn action-btn-details w-full">
									<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
									</svg>
									View Details
								</a>
							}
						</div>
					</div>
				</div>
			}
		</div>

		@if (!Model.Machines.Any())
		{
			<div class="empty-state">
				<h2>No machines found</h2>
				<p>No CMMs match your current search criteria. Try adjusting your filters or search terms.</p>
			</div>
		}

		<!-- Pagination -->
		@if (Model.Machines.Any() && maxPage > 1)
		{
			<div class="pagination-container">
				<nav aria-label="Machines pagination">
					<ul class="pagination">
						@if (Model.CurrentPage > 1)
						{
							<li class="page-item">
								<a class="page-link"
								   asp-action="All"
								   asp-route-currentPage="@previousPage"
								   asp-route-search="@Model.Search"
								   asp-route-sorting="@Model.Sorting">
									<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
									</svg>
									Previous
								</a>
							</li>
						}

						@for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min((int)maxPage, Model.CurrentPage + 2); i++)
						{
							<li class="page-item @(i == Model.CurrentPage ? "active" : "")">
								<a class="page-link"
								   asp-action="All"
								   asp-route-currentPage="@i"
								   asp-route-search="@Model.Search"
								   asp-route-sorting="@Model.Sorting">@i</a>
							</li>
						}

						@if (Model.CurrentPage < maxPage)
						{
							<li class="page-item">
								<a class="page-link"
								   asp-action="All"
								   asp-route-currentPage="@(Model.CurrentPage + 1)"
								   asp-route-search="@Model.Search"
								   asp-route-sorting="@Model.Sorting">
									Next
									<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
									</svg>
								</a>
							</li>
						}
					</ul>
				</nav>
			</div>
		}
	}
</div>

@section Scripts {
	<script src="~/js/pages/machines.js" asp-append-version="true"></script>
	<partial name="_ValidationScriptsPartial" />
}
