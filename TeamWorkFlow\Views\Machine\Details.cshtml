﻿@model MachineDetailsServiceModel

@{
	ViewBag.Title = "Machine Details";
}

@section Styles {
	<link rel="stylesheet" href="~/css/pages/details.css?v=@DateTime.Now.Ticks" />
}

<div class="details-container">


	<!-- Header Section -->
	<div class="details-header">
		<h1 class="details-title">CMM Specifications</h1>
		<p class="details-subtitle">Comprehensive machine information including calibration status, capacity metrics, and operational data</p>
	</div>

	<!-- Main Content -->
	<div class="details-content has-image">
		<!-- Machine Image -->
		<div class="details-image-container">
			@if (!string.IsNullOrEmpty(Model.ImageUrl))
			{
				<img src="@Model.ImageUrl" alt="@Model.Name" class="details-image" />
			}
			else
			{
				<div class="details-image-placeholder">
					<svg fill="none" stroke="currentColor" viewBox="0 0 24 24" width="48" height="48">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
					</svg>
					<span>No image available</span>
				</div>
			}
		</div>

		<!-- Machine Details -->
		<div class="details-card">
			<div class="details-card-header">
				<div class="details-id">CMM #@Model.Id</div>
				<h2 class="details-name">@Model.Name</h2>
			</div>

			<div class="details-card-body">
				<div class="details-grid">
					<!-- Calibration Information -->
					<div class="details-section">
						<h3 class="details-section-title">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
							</svg>
							Calibration Status
						</h3>
						<div class="detail-item">
							<span class="detail-label">
								<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
								</svg>
								Next Calibration
							</span>
							<span class="detail-value"><strong style="color: #dc2626;">@Model.CalibrationSchedule</strong></span>
						</div>
						<div class="detail-item">
							<span class="detail-label">
								<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
								Calibration Status
							</span>
							<span class="detail-value">
								@{
									string calibrationClass = Model.IsCalibrated ? "status-active" : "status-inactive";
									string calibrationText = Model.IsCalibrated ? "Calibrated" : "Needs Calibration";
								}
								<span class="status-badge @calibrationClass">
									<span class="status-indicator"></span>
									@calibrationText
								</span>
							</span>
						</div>
					</div>

					<!-- Capacity & Performance -->
					<div class="details-section">
						<h3 class="details-section-title">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
							</svg>
							Capacity & Performance
						</h3>
						<div class="detail-item">
							<span class="detail-label">
								<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
								</svg>
								Machine Capacity
							</span>
							<span class="detail-value"><strong>@Model.Capacity</strong> hours/day</span>
						</div>
						<div class="detail-item">
							<span class="detail-label">
								<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
								</svg>
								Total Load
							</span>
							<span class="detail-value"><strong>@Model.TotalMachineLoad</strong> since last calibration</span>
						</div>
					</div>

					<!-- Assignment Information -->
					<div class="details-section assignment-section">
						<h3 class="details-section-title">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
							</svg>
							Current Assignment
						</h3>

						@if (Model.IsOccupied)
						{
							<div class="assignment-status occupied">
								<div class="assignment-header">
									<svg class="assignment-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
									</svg>
									<span class="status-text">Machine is currently occupied</span>
								</div>

								<div class="assignment-details">
									<div class="detail-item">
										<span class="detail-label">
											<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
											</svg>
											Assigned Task
										</span>
										<span class="detail-value">
											<a href="@Url.Action("Details", "Task", new { id = Model.AssignedTaskId, extension = "task-details" })" class="task-link">
												@Model.AssignedTaskName
											</a>
										</span>
									</div>

									<div class="detail-item">
										<span class="detail-label">
											<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
											</svg>
											Project Number
										</span>
										<span class="detail-value"><strong>#@Model.AssignedTaskProjectNumber</strong></span>
									</div>

									@if (!string.IsNullOrEmpty(Model.AssignedTaskDescription))
									{
										<div class="detail-item">
											<span class="detail-label">
												<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
												</svg>
												Description
											</span>
											<span class="detail-value">@Model.AssignedTaskDescription</span>
										</div>
									}

									@if (!string.IsNullOrEmpty(Model.TaskStatus))
									{
										<div class="detail-item">
											<span class="detail-label">
												<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
												</svg>
												Task Status
											</span>
											<span class="detail-value">
												<span class="status-badge <EMAIL>().Replace(" ", "-")">
													<span class="status-indicator"></span>
													@Model.TaskStatus
												</span>
											</span>
										</div>
									}

									@if (!string.IsNullOrEmpty(Model.AssignedTaskPriority))
									{
										<div class="detail-item">
											<span class="detail-label">
												<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
												</svg>
												Priority
											</span>
											<span class="detail-value">
												<span class="priority-badge <EMAIL>()">@Model.AssignedTaskPriority</span>
											</span>
										</div>
									}

									@if (!string.IsNullOrEmpty(Model.AssignedTaskDeadline))
									{
										<div class="detail-item">
											<span class="detail-label">
												<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
												</svg>
												Deadline
											</span>
											<span class="detail-value"><strong style="color: #dc2626;">@Model.AssignedTaskDeadline</strong></span>
										</div>
									}

									@if (Model.AssignedOperators.Any())
									{
										<div class="detail-item operators-section">
											<span class="detail-label">
												<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
												</svg>
												Assigned Operators
											</span>
											<div class="operators-list">
												@foreach (var op in Model.AssignedOperators)
												{
													<div class="operator-card">
														<div class="operator-info">
															<div class="operator-name">@op.OperatorName</div>
															<div class="operator-email">@op.OperatorEmail</div>
														</div>
													</div>
												}
											</div>
										</div>
									}
								</div>
							</div>
						}
						else
						{
							<div class="assignment-status available">
								<div class="assignment-header">
									<svg class="assignment-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
									</svg>
									<span class="status-text">Machine is available for assignment</span>
								</div>
								<div class="availability-details">
									@if (Model.IsCalibrated)
									{
										<p class="availability-message success">
											This machine is calibrated and ready to be assigned to a new task.
										</p>
									}
									else
									{
										<p class="availability-message warning">
											<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
											</svg>
											Machine requires calibration before it can be assigned to tasks.
										</p>
									}
								</div>
							</div>
						}
					</div>
				</div>

				<!-- Action Buttons -->
				@if (User.Identity?.IsAuthenticated == true && User.IsAdmin())
				{
					<div class="details-actions">
						<a href="@Url.Action("All", "Machine")" class="details-btn details-btn-back">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
							</svg>
							Back to CMMs
						</a>
						<a href="@Url.Action("Edit", "Machine", new { id = Model.Id, extension = Model.GetMachineExtension() })" class="details-btn details-btn-edit">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
							</svg>
							Edit Machine
						</a>
						<a href="@Url.Action("Delete", "Machine", new { id = Model.Id, extension = Model.GetMachineExtension() })" class="details-btn details-btn-delete">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
							</svg>
							Delete Machine
						</a>
					</div>
				}
			</div>
		</div>
	</div>
</div>

@section Scripts {
	<script src="~/js/pages/details.js?v=@DateTime.Now.Ticks"></script>
}
