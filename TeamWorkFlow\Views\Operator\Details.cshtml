﻿@using TeamWorkFlow.Core.Extensions
@model OperatorDetailsServiceModel

@{
	ViewBag.Title = "Operator Details";
}

@section Styles {
	<link rel="stylesheet" href="~/css/pages/details.css?v=@DateTime.Now.Ticks" />
}

<div class="details-container">


	<!-- Header Section -->
	<div class="details-header">
		<h1 class="details-title">Operator Profile</h1>
		<p class="details-subtitle">Complete operator information including contact details, availability status, and performance metrics</p>
	</div>

	<!-- Main Content -->
	<div class="details-content">
		<div class="details-card">
			<div class="details-card-header">
				<div class="details-id">Operator #@Model.Id</div>
				<h2 class="details-name">@Model.FullName</h2>
			</div>

			<div class="details-card-body">
				<div class="details-grid">
					<!-- Contact Information -->
					<div class="details-section">
						<h3 class="details-section-title">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
							</svg>
							Contact Information
						</h3>
						<div class="detail-item">
							<span class="detail-label">
								<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
								</svg>
								Email Address
							</span>
							<span class="detail-value" data-copyable><strong>@Model.Email</strong></span>
						</div>
						<div class="detail-item">
							<span class="detail-label">
								<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
								</svg>
								Phone Number
							</span>
							<span class="detail-value" data-copyable>@Model.PhoneNumber</span>
						</div>
					</div>

					<!-- Status & Availability -->
					<div class="details-section">
						<h3 class="details-section-title">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
							</svg>
							Status & Availability
						</h3>
						<div class="detail-item">
							<span class="detail-label">
								<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
								Availability Status
							</span>
							<span class="detail-value">
								@{
									string statusClass = Model.AvailabilityStatus.ToLower() switch
									{
										"available" => "status-active",
										"busy" => "status-pending",
										"unavailable" => "status-inactive",
										_ => "status-inactive"
									};
								}
								<span class="status-badge @statusClass">
									<span class="status-indicator"></span>
									@Model.AvailabilityStatus
								</span>
							</span>
						</div>
						<div class="detail-item">
							<span class="detail-label">
								<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.636 18.364a9 9 0 010-12.728m12.728 0a9 9 0 010 12.728m-9.9-2.829a5 5 0 010-7.07m7.072 0a5 5 0 010 7.07M13 12a1 1 0 11-2 0 1 1 0 012 0z"></path>
								</svg>
								Account Status
							</span>
							<span class="detail-value">
								@{
									string activeClass = Model.IsActive ? "status-active" : "status-inactive";
									string activeText = Model.IsActive ? "Active" : "Inactive";
								}
								<span class="status-badge @activeClass">
									<span class="status-indicator"></span>
									@activeText
								</span>
							</span>
						</div>
					</div>

					<!-- Capacity & Performance -->
					<div class="details-section">
						<h3 class="details-section-title">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
							</svg>
							Capacity & Performance
						</h3>
						<div class="detail-item">
							<span class="detail-label">
								<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
								Daily Capacity
							</span>
							<span class="detail-value">
								<strong>@Model.GetCapacityDisplay()</strong>
								<span style="color: #6b7280; font-weight: normal;">(@Model.Capacity hours per day)</span>
							</span>
						</div>
						<div class="detail-item">
							<span class="detail-label">
								<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
								</svg>
								Current Tasks
							</span>
							<span class="detail-value">@Model.CurrentTasks tasks</span>
						</div>
						<div class="detail-item">
							<span class="detail-label">
								<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
								Completed Tasks
							</span>
							<span class="detail-value"><strong>@Model.TotalCompletedTasks</strong> total</span>
						</div>
					</div>
				</div>

				<!-- Action Buttons -->
				@if (User.Identity?.IsAuthenticated == true && User.IsAdmin())
				{
					<div class="details-actions">
						<a href="@Url.Action("All", "Operator")" class="details-btn details-btn-back">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
							</svg>
							Back to Operators
						</a>
						<a href="@Url.Action("Edit", "Operator", new { id = Model.Id, extension = Model.GetOperatorExtension() })" class="details-btn details-btn-edit">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
							</svg>
							Edit Operator
						</a>
						<a href="@Url.Action("Delete", "Operator", new { id = Model.Id, extension = Model.GetOperatorExtension() })" class="details-btn details-btn-delete">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
							</svg>
							Delete Operator
						</a>
					</div>
				}
			</div>
		</div>
	</div>
</div>

@section Scripts {
	<script src="~/js/pages/details.js?v=@DateTime.Now.Ticks"></script>
}
