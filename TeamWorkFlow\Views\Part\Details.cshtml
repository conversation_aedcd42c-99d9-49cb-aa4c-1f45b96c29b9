﻿@model PartDetailsServiceModel

@{
	ViewBag.Title = "Part Details";
}

@section Styles {
	<link rel="stylesheet" href="~/css/pages/details.css?v=@DateTime.Now.Ticks" />
}

<div class="details-container">


	<!-- Header Section -->
	<div class="details-header">
		<h1 class="details-title">Part Specifications</h1>
		<p class="details-subtitle">Detailed part information including technical specifications, project association, and status tracking</p>
	</div>

	<!-- Main Content -->
	<div class="details-content has-image">
		<!-- Part Image -->
		<div class="details-image-container">
			@if (!string.IsNullOrEmpty(Model.ImageUrl))
			{
				<img src="@Model.ImageUrl" alt="@Model.Name" class="details-image" />
			}
			else
			{
				<div class="details-image-placeholder">
					<svg fill="none" stroke="currentColor" viewBox="0 0 24 24" width="48" height="48">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
					</svg>
					<span>No image available</span>
				</div>
			}
		</div>

		<!-- Part Details -->
		<div class="details-card">
			<div class="details-card-header">
				<div class="details-id">Part #@Model.Id</div>
				<h2 class="details-name">@Model.Name</h2>
			</div>

			<div class="details-card-body">
				<div class="details-grid">
					<!-- Project Information -->
					<div class="details-section">
						<h3 class="details-section-title">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
							</svg>
							Project Association
						</h3>
						<div class="detail-item">
							<span class="detail-label">
								<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
								</svg>
								Project Number
							</span>
							<span class="detail-value" data-copyable><strong>@Model.ProjectNumber</strong></span>
						</div>
						<div class="detail-item">
							<span class="detail-label">
								<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
								Status
							</span>
							<span class="detail-value">
								@{
									string statusClass = Model.Status.ToLower() switch
									{
										"completed" => "status-completed",
										"active" => "status-active",
										"in progress" => "status-in-progress",
										"pending" => "status-pending",
										_ => "status-inactive"
									};
								}
								<span class="status-badge @statusClass">
									<span class="status-indicator"></span>
									@Model.Status
								</span>
							</span>
						</div>
					</div>

					<!-- Part Specifications -->
					<div class="details-section">
						<h3 class="details-section-title">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
							</svg>
							Part Specifications
						</h3>
						<div class="detail-item">
							<span class="detail-label">
								<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
								</svg>
								Article Number
							</span>
							<span class="detail-value" data-copyable>@Model.PartArticleNumber</span>
						</div>
						<div class="detail-item">
							<span class="detail-label">
								<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
								</svg>
								Client Number
							</span>
							<span class="detail-value" data-copyable>@Model.PartClientNumber</span>
						</div>
					</div>
				</div>

				<!-- Action Buttons -->
				@if (User.Identity?.IsAuthenticated == true && User.IsAdmin())
				{
					<div class="details-actions">
						<a href="@Url.Action("All", "Part")" class="details-btn details-btn-back">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
							</svg>
							Back to Parts
						</a>
						<a href="@Url.Action("Edit", "Part", new { id = Model.Id, extension = Model.GetPartExtension() })" class="details-btn details-btn-edit">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
							</svg>
							Edit Part
						</a>
						<a href="@Url.Action("Delete", "Part", new { id = Model.Id, extension = Model.GetPartExtension() })" class="details-btn details-btn-delete">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
							</svg>
							Delete Part
						</a>
					</div>
				}
			</div>
		</div>
	</div>
</div>

@section Scripts {
	<script src="~/js/pages/details.js?v=@DateTime.Now.Ticks"></script>
}
