﻿<!DOCTYPE html>
<html lang="en-GB">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Language" content="en-GB" />
    <title>@ViewData["Title"] - TeamWorkFlow</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            corePlugins: {
                preflight: false,
            }
        }
    </script>
    <link rel="stylesheet" href="~/lib/toastr/toastr.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/style.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/styles.css" />
    <link rel="stylesheet" href="~/css/navigation-fix.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/responsive-header.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/modern-forms.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/TeamWorkFlow.styles.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/navigation-override.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/enhanced-navigation.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/mobile-navigation.css" asp-append-version="true" />
    @await RenderSectionAsync("Styles", required: false)
</head>
@{
    var currentPath = ViewContext.HttpContext.Request.Path.Value?.ToLower();

    // Only consider login and register pages as auth pages, not profile/manage pages
    var isAuthPage = !string.IsNullOrEmpty(currentPath) &&
                    (currentPath.Contains("/identity/account/login") ||
                     currentPath.Contains("/identity/account/register") ||
                     currentPath.Contains("/account/login") ||
                     currentPath.Contains("/account/register"));
}
<body class="@(isAuthPage ? "auth-page" : "")">
    <header>
        <nav class="navbar navbar-expand-lg navbar-light border-bottom box-shadow mb-3">
            <div class="container-fluid">


                @if (!isAuthPage)
                {
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-collapse" aria-controls="navbar-collapse"
                            aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                }

                <div class="navbar-collapse collapse" id="navbar-collapse">
                    @await Component.InvokeAsync("ConditionalNavigation")
	                <partial name="_LoginPartial"/>
                </div>
            </div>
        </nav>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted">
        <div class="container">
            &copy; @DateTime.Now.Year - TeamWorkFlow
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
	<script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/lib/toastr/toastr.min.js"></script>
    <script src="~/js/responsive-header.js" asp-append-version="true"></script>
    <script src="~/js/modern-forms.js" asp-append-version="true"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script>
        // Ensure navigation is visible on page load (desktop only)
        document.addEventListener('DOMContentLoaded', function() {
            const navbar = document.querySelector('.navbar-collapse');
            const navLinks = document.querySelectorAll('.navbar-nav');

            // Only apply visibility fixes on desktop screens (992px and above)
            function applyDesktopNavigation() {
                if (window.innerWidth >= 992) {
                    if (navbar) {
                        navbar.style.display = 'flex';
                        navbar.style.visibility = 'visible';
                        navbar.style.opacity = '1';
                    }

                    navLinks.forEach(function(nav) {
                        nav.style.display = 'flex';
                        nav.style.visibility = 'visible';
                        nav.style.opacity = '1';
                    });
                } else {
                    // On mobile, let Bootstrap handle the collapse behavior
                    if (navbar) {
                        navbar.style.display = '';
                        navbar.style.visibility = '';
                        navbar.style.opacity = '';
                    }

                    navLinks.forEach(function(nav) {
                        nav.style.display = '';
                        nav.style.visibility = '';
                        nav.style.opacity = '';
                    });
                }
            }

            // Apply on load
            applyDesktopNavigation();

            // Apply on window resize
            window.addEventListener('resize', applyDesktopNavigation);
        });
    </script>
    @await RenderSectionAsync("Scripts", required: false)
    @if (TempData[Messages.UserMessageSuccess] != null)
    {
	    <script defer>
            message.showSuccess('@TempData[Messages.UserMessageSuccess]')
	    </script>
    }
    @if (TempData[Messages.UserMessageError] != null)
    {
	    <script defer>
            message.showError('@TempData[Messages.UserMessageSuccess]')
	    </script>
    }
</body>
</html>
