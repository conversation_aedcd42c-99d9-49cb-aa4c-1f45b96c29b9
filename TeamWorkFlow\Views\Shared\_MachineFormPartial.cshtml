﻿@model MachineFormModel

<div class="modern-form-container">
	<div class="form-header">
		<h1 class="form-title">Machine Management</h1>
		<p class="form-subtitle">Create or update CMM machine information and calibration schedule</p>
	</div>

	<form method="post" id="machine-form">
		@Html.AntiForgeryToken()
		<div class="modern-form-grid two-columns">
			<div class="modern-form-group">
				<label asp-for="@Model.Name" class="modern-form-label">Machine Name</label>
				<input asp-for="@Model.Name" class="modern-form-input"
					   required
					   minlength="3"
					   maxlength="50"
					   placeholder="Enter machine name..." />
				<span asp-validation-for="@Model.Name" class="modern-validation-message"></span>
			</div>

			<div class="modern-form-group">
				<label asp-for="@Model.Capacity" class="modern-form-label">Machine Capacity (hours/day)</label>
				<input asp-for="@Model.Capacity" class="modern-form-input"
					   type="number"
					   required
					   min="1"
					   max="24"
					   placeholder="Enter capacity in hours per day..." />
				<span asp-validation-for="@Model.Capacity" class="modern-validation-message"></span>
			</div>
		</div>

		<div class="modern-form-grid two-columns">
			<div class="modern-form-group">
				<label asp-for="@Model.CalibrationSchedule" class="modern-form-label">Next Calibration Date</label>
				<input asp-for="@Model.CalibrationSchedule" class="modern-form-input future-date" type="date" aria-required="true" />
				<span asp-validation-for="@Model.CalibrationSchedule" class="modern-validation-message"></span>
			</div>

			<div class="modern-form-group">
				<label asp-for="@Model.IsCalibrated" class="modern-form-label">Calibration Status</label>
				<select asp-for="@Model.IsCalibrated" class="modern-form-select" required>
					<option disabled selected value="">Select Status...</option>
					<option value="true">Calibrated</option>
					<option value="false">Not Calibrated</option>
				</select>
				<span asp-validation-for="@Model.IsCalibrated" class="modern-validation-message"></span>
			</div>
		</div>

		<div class="modern-form-group">
			<label asp-for="@Model.ImageUrl" class="modern-form-label">Machine Image URL</label>
			<input asp-for="@Model.ImageUrl" class="modern-form-input"
				   type="url"
				   required
				   minlength="10"
				   maxlength="300"
				   placeholder="Enter image URL..." />
			<span asp-validation-for="@Model.ImageUrl" class="modern-validation-message"></span>
		</div>

		<div class="modern-form-actions">
			<button type="submit" class="modern-submit-btn">
				Save Machine
			</button>
			<a href="@Url.Action("All", "Machine")" class="modern-cancel-btn">
				Cancel
			</a>
		</div>
	</form>
</div>
