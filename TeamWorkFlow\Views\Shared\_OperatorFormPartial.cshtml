﻿@model OperatorFormModel

<div class="modern-form-container">
	<div class="form-header">
		<h1 class="form-title">Operator Management</h1>
		<p class="form-subtitle">Create or update operator information and availability status</p>
	</div>

	<form method="post" id="operator-form">
		@Html.AntiForgeryToken()
		<div class="modern-form-grid two-columns">
			<div class="modern-form-group">
				<label asp-for="@Model.FullName" class="modern-form-label">Full Name</label>
				<input asp-for="@Model.FullName" class="modern-form-input" aria-required="true" placeholder="Enter full name..." />
				<span asp-validation-for="@Model.FullName" class="modern-validation-message"></span>
			</div>

			<div class="modern-form-group">
				<label asp-for="@Model.Email" class="modern-form-label">Email Address</label>
				<input asp-for="@Model.Email" class="modern-form-input" type="email" aria-required="true" placeholder="Enter email address..." />
				<span asp-validation-for="@Model.Email" class="modern-validation-message"></span>
			</div>
		</div>

		<div class="modern-form-grid two-columns">
			<div class="modern-form-group">
				<label asp-for="@Model.PhoneNumber" class="modern-form-label">Phone Number</label>
				<input asp-for="@Model.PhoneNumber" class="modern-form-input" type="tel" aria-required="true" placeholder="Enter phone number..." />
				<span asp-validation-for="@Model.PhoneNumber" class="modern-validation-message"></span>
			</div>

			<div class="modern-form-group">
				<label asp-for="@Model.Capacity" class="modern-form-label">
					Working Hours per Day
					<small style="color: #6b7280; font-weight: normal; display: block; margin-top: 2px;">
						Enter hours (4-12). 9 hours = 100% capacity
					</small>
				</label>
				<input asp-for="@Model.Capacity" class="modern-form-input" type="number" min="4" max="12" aria-required="true" placeholder="Enter working hours (4-12)..." />
				<span asp-validation-for="@Model.Capacity" class="modern-validation-message"></span>
			</div>
		</div>

		<div class="modern-form-grid two-columns">
			<div class="modern-form-group">
				<label asp-for="@Model.IsActive" class="modern-form-label">Active Status</label>
				<select asp-for="@Model.IsActive" class="modern-form-select" aria-required="true">
					<option disabled selected value="">Select Status...</option>
					<option value="true">Active</option>
					<option value="false">Inactive</option>
				</select>
				<span asp-validation-for="@Model.IsActive" class="modern-validation-message"></span>
			</div>

			<div class="modern-form-group">
				<label asp-for="@Model.AvailabilityStatusId" class="modern-form-label">
					Availability Status
					<small style="color: #6b7280; font-weight: normal; display: block; margin-top: 2px;">
						Only "at work" status allows operator to be active
					</small>
				</label>
				<select asp-for="@Model.AvailabilityStatusId" class="modern-form-select" aria-required="true" id="availabilityStatus">
					<option disabled selected value="">Select Availability...</option>
					@foreach (var s in Model.AvailabilityStatusModels)
					{
						<option value="@s.Id">@s.Name</option>
					}
				</select>
				<span asp-validation-for="@Model.AvailabilityStatusId" class="modern-validation-message"></span>
				<div id="statusWarning" style="display: none; color: #f59e0b; font-size: 0.875rem; margin-top: 4px;">
					⚠️ Operators with this status will be automatically marked as inactive
				</div>
			</div>
		</div>

		<div class="modern-form-actions">
			<button type="submit" class="modern-submit-btn">
				Save Operator
			</button>
			<a href="@Url.Action("All", "Operator")" class="modern-cancel-btn">
				Cancel
			</a>
		</div>
	</form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const availabilityStatus = document.getElementById('availabilityStatus');
    const statusWarning = document.getElementById('statusWarning');
    const activeStatusSelect = document.querySelector('select[name="IsActive"]');

    if (availabilityStatus && statusWarning) {
        availabilityStatus.addEventListener('change', function() {
            const selectedValue = parseInt(this.value);
            const atWorkStatusId = 1; // "at work" status ID

            if (selectedValue && selectedValue !== atWorkStatusId) {
                statusWarning.style.display = 'block';
                // Automatically set to inactive if not "at work"
                if (activeStatusSelect) {
                    activeStatusSelect.value = 'false';
                }
            } else {
                statusWarning.style.display = 'none';
            }
        });
    }
});
</script>
