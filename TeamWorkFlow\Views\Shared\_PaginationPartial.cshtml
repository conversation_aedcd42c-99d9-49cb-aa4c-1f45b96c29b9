@model TeamWorkFlow.Core.Models.Pager.PagerServiceModel

@if (Model != null && Model.TotalPages > 1)
{
    <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">
            @if (Model.CurrentPage > 1)
            {
                <li class="page-item">
                    <a class="page-link" asp-route-page="@(Model.CurrentPage - 1)">Previous</a>
                </li>
            }
            @for (int i = Model.StartPage; i <= Model.EndPage; i++)
            {
                <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                    <a class="page-link" asp-route-page="@i">@i</a>
                </li>
            }
            @if (Model.CurrentPage < Model.TotalPages)
            {
                <li class="page-item">
                    <a class="page-link" asp-route-page="@(Model.CurrentPage + 1)">Next</a>
                </li>
            }
        </ul>
    </nav>
}
