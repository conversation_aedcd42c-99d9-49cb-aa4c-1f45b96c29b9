﻿@model PartServiceModel

<div class="col-md-4">
    <div class="card mb-3">
        <img class="card-img-top" src="@Model.ImageUrl" alt="Part Image">
        <div class="card-body text-center">
            <h4>@Model.Name - Project: @Model.ProjectNumber</h4>
            <h6>Article number: <b>@Model.PartArticleNumber</b></h6>
            <h6>Article number client: <b>@Model.PartClientNumber</b></h6>
            <h6>Tool No.: <b>@Model.ToolNumber</b></h6>
            <h6>3D-model: @Model.PartModel</h6>
            <br />
            
	        @if (User.IsAdmin())
	        {
		        <a asp-controller="Part" asp-action="Details" asp-route-id="@Model.Id"
		           class="btn btn-success" asp-route-extension="@Model.GetPartExtension()">Details</a>
		        <a asp-controller="Part" asp-action="Edit" asp-route-id="@Model.Id"
		           class="btn btn-warning" asp-route-extension="@Model.GetPartExtension()">Edit</a>
		        <a asp-controller="Part" asp-action="Delete" asp-route-id="@Model.Id"
		           class="btn btn-danger" asp-route-extension="@Model.GetPartExtension()">Delete</a>

		        <p></p>
	        }

	        else if (User.IsOperator())
	        {
		        <a asp-controller="Part" asp-action="Details" asp-route-id="@Model.Id"
		           class="btn btn-success" asp-route-extension="@Model.GetPartExtension()">Details</a>
	        }
        </div>
    </div>
</div>