@model ProjectFormModel

<div class="row">
    <div class="col-sm-12 offset-lg-2 col-lg-8 offset-xl-3 col-xl-6">
        <form method="post">
            @Html.AntiForgeryToken()
            <div class="form-group">
                <label asp-for="ProjectName"></label>
                <input asp-for="ProjectName" class="form-control" placeholder="Name of your project...">
                <span asp-validation-for="ProjectName" class="small text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="ProjectNumber"></label>
                <input asp-for="ProjectNumber" class="form-control" placeholder="Project number...">
                <span asp-validation-for="ProjectNumber" class="small text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="ClientName"></label>
                <input asp-for="ClientName" class="form-control" placeholder="Client name...">
                <span asp-validation-for="ClientName" class="small text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Appliance"></label>
                <input asp-for="Appliance" class="form-control" placeholder="Area of appliance...">
                <span asp-validation-for="Appliance" class="small text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="TotalHoursSpent"></label>
                <input asp-for="TotalHoursSpent" class="form-control" placeholder="Total hours spent on the project...">
                <span asp-validation-for="TotalHoursSpent" class="small text-danger"></span>
            </div>
           
            <div class="form-group">
                <label asp-for="ProjectStatusId"></label>
                <select asp-for="ProjectStatusId" class="form-control">
                    @foreach (var status in Model.ProjectStatuses)
                    {
                        <option value="@status.Id">@status.Name</option>
                    }
                </select>
                <span asp-validation-for="ProjectStatusId" class="small text-danger"></span>
            </div>

            <div class="text-center">
                <input class="btn btn-primary mt-3" type="submit" value="Apply" />
            </div>
        </form>
    </div>
</div>
