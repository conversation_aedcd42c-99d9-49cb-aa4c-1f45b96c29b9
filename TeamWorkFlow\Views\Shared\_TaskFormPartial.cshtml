﻿@model TaskFormModel

<div class="modern-form-container">
	<div class="form-header">
		<h1 class="form-title">Task Management</h1>
		<p class="form-subtitle">Create or update task information with all necessary details</p>
	</div>

	<form method="post" id="task-form">
		@Html.AntiForgeryToken()
		<div class="modern-form-grid two-columns">
			<div class="modern-form-group">
				<label asp-for="@Model.ProjectNumber" class="modern-form-label">Project Number</label>
				<input asp-for="@Model.ProjectNumber" class="modern-form-input"
					   required
					   minlength="6"
					   maxlength="10"
					   pattern="[0-9]+"
					   title="Project number must be 6-10 digits"
					   placeholder="Enter project number..." />
				<span asp-validation-for="@Model.ProjectNumber" class="modern-validation-message"></span>
			</div>

			<div class="modern-form-group">
				<label asp-for="@Model.Name" class="modern-form-label">Task Name</label>
				<input asp-for="@Model.Name" class="modern-form-input"
					   required
					   minlength="5"
					   maxlength="100"
					   placeholder="Enter task name..." />
				<span asp-validation-for="@Model.Name" class="modern-validation-message"></span>
			</div>
		</div>

		<div class="modern-form-group">
			<label asp-for="@Model.Description" class="modern-form-label">Task Description</label>
			<textarea asp-for="@Model.Description" class="modern-form-textarea"
					  required
					  minlength="5"
					  maxlength="1500"
					  rows="4"
					  placeholder="Describe the task in detail..."></textarea>
			<span asp-validation-for="@Model.Description" class="modern-validation-message"></span>
		</div>

		<div class="modern-form-grid two-columns">
			<div class="modern-form-group">
				<label asp-for="@Model.StartDate" class="modern-form-label">Start Date</label>
				<input asp-for="@Model.StartDate" class="modern-form-input" type="date" />
				<span asp-validation-for="@Model.StartDate" class="modern-validation-message"></span>
			</div>

			<div class="modern-form-group">
				<label asp-for="@Model.EndDate" class="modern-form-label">End Date</label>
				<input asp-for="@Model.EndDate" class="modern-form-input future-date" type="date" />
				<span asp-validation-for="@Model.EndDate" class="modern-validation-message"></span>
			</div>
		</div>

		<div class="modern-form-group">
			<label asp-for="@Model.Deadline" class="modern-form-label">Task Deadline</label>
			<input asp-for="@Model.Deadline" class="modern-form-input future-date" type="date" />
			<span asp-validation-for="@Model.Deadline" class="modern-validation-message"></span>
		</div>

		<div class="modern-form-grid two-columns">
			<div class="modern-form-group">
				<label asp-for="@Model.PriorityId" class="modern-form-label">Task Priority</label>
				<select asp-for="@Model.PriorityId" class="modern-form-select" required>
					<option disabled selected value="">Select Priority...</option>
					@foreach (var p in Model.Priorities)
					{
						<option value="@p.Id">@p.Name</option>
					}
				</select>
				<span asp-validation-for="@Model.PriorityId" class="modern-validation-message"></span>
			</div>

			<div class="modern-form-group">
				<label asp-for="@Model.StatusId" class="modern-form-label">Task Status</label>
				<select asp-for="@Model.StatusId" class="modern-form-select" required>
					<option disabled selected value="">Select Status...</option>
					@foreach (var s in Model.Statuses)
					{
						<option value="@s.Id">@s.Name</option>
					}
				</select>
				<span asp-validation-for="@Model.StatusId" class="modern-validation-message"></span>
			</div>

			<div class="modern-form-group">
				<label asp-for="@Model.EstimatedTime" class="modern-form-label">Estimated Time (Hours)</label>
				<input asp-for="@Model.EstimatedTime" class="modern-form-input"
					   type="number"
					   min="1"
					   max="1000"
					   required
					   placeholder="Enter estimated hours..." />
				<span asp-validation-for="@Model.EstimatedTime" class="modern-validation-message"></span>
			</div>
		</div>

		<div class="modern-form-actions">
			<button type="submit" class="modern-submit-btn">
				Save Task
			</button>
			<a href="@Url.Action("All", "Task")" class="modern-cancel-btn">
				Cancel
			</a>
		</div>
	</form>
</div>
