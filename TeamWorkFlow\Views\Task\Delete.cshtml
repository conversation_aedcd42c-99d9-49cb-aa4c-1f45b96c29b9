﻿@model TaskDeleteServiceModel
@{
	ViewBag.Title = "Delete Task";
}

@section Styles {
    <link rel="stylesheet" href="~/css/pages/delete.css" />
}

<div class="delete-container fade-in-delete">
    <!-- Header Section -->
    <div class="delete-header">
        <h1 class="delete-title">Delete Task</h1>
        <p class="delete-subtitle">Permanently remove task from system</p>
    </div>

    <!-- Main Content Card -->
    <div class="delete-card slide-in-delete">
        <!-- Warning Banner -->
        <div class="delete-warning-banner">
            <div class="delete-warning-icon">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <p class="delete-warning-text">Warning: This action cannot be undone</p>
            <p class="delete-warning-subtext">All data associated with this task will be permanently deleted</p>
        </div>

        <!-- Content Section -->
        <div class="delete-content">
            <div class="delete-item-preview">
                <h2 class="delete-item-title">@Model.Name</h2>

                <div class="delete-item-details">
                    <div class="delete-detail-row">
                        <span class="delete-detail-label">Task Name:</span>
                        <span class="delete-detail-value">@Model.Name</span>
                    </div>
                    <div class="delete-detail-row">
                        <span class="delete-detail-label">Created by:</span>
                        <span class="delete-detail-value">@Model.Creator</span>
                    </div>
                    @if (!string.IsNullOrEmpty(Model.Description))
                    {
                        <div class="delete-detail-row">
                            <span class="delete-detail-label">Description:</span>
                            <span class="delete-detail-value">@Model.Description</span>
                        </div>
                    }
                </div>
            </div>

            <!-- Confirmation Message -->
            <div class="delete-confirmation-message">
                <p class="delete-confirmation-text">Are you sure you want to delete "@Model.Name"?</p>
                <p class="delete-confirmation-subtext">This task and all its associated data will be permanently removed from the system.</p>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="delete-actions">
            <form asp-action="Confirmation" method="post" style="display: inline;">
                @Html.AntiForgeryToken()
                <input type="hidden" asp-for="Id" />
                <button type="submit" class="delete-btn delete-btn-confirm" onclick="return confirmDelete(this)">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Confirm Delete
                </button>
            </form>
            <a asp-action="All" class="delete-btn delete-btn-cancel">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Cancel
            </a>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/pages/delete.js"></script>
}
