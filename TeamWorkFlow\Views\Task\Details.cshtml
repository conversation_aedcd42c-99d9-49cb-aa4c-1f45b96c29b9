﻿@model TaskDetailsServiceModel

@{
	ViewBag.Title = "Task Details";
}

@section Styles {
	<link rel="stylesheet" href="~/css/pages/details.css?v=@DateTime.Now.Ticks" />
}

<div class="details-container">


	<!-- Header Section -->
	<div class="details-header">
		<h1 class="details-title">Task Details</h1>
		<p class="details-subtitle">Complete information about this task including timeline, assignments, and progress tracking</p>
	</div>

	<!-- Main Content -->
	<div class="details-content">
		<div class="details-card">
			<div class="details-card-header">
				<div class="details-id">Task #@Model.Id</div>
				<h2 class="details-name">@Model.Name</h2>
			</div>

			<div class="details-card-body">
				<div class="details-grid">
					<!-- Project Information -->
					<div class="details-section">
						<h3 class="details-section-title">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
							</svg>
							Project Information
						</h3>
						<div class="detail-item">
							<span class="detail-label">
								<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
								</svg>
								Project Number
							</span>
							<span class="detail-value" data-copyable><strong>@Model.ProjectNumber</strong></span>
						</div>
						<div class="detail-item">
							<span class="detail-label">
								<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
								</svg>
								Creator
							</span>
							<span class="detail-value">@Model.Creator</span>
						</div>
					</div>

					<!-- Task Status & Priority -->
					<div class="details-section">
						<h3 class="details-section-title">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
							</svg>
							Status & Priority
						</h3>
						<div class="detail-item">
							<span class="detail-label">
								<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
								Status
							</span>
							<span class="detail-value">
								@{
									string statusClass = Model.Status.ToLower() switch
									{
										"completed" => "status-completed",
										"in progress" => "status-in-progress",
										"pending" => "status-pending",
										_ => "status-inactive"
									};
								}
								<span class="status-badge @statusClass">
									<span class="status-indicator"></span>
									@Model.Status
								</span>
							</span>
						</div>
						<div class="detail-item">
							<span class="detail-label">
								<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
								Priority
							</span>
							<span class="detail-value">
								@{
									string priorityClass = Model.Priority.ToLower() switch
									{
										"low" => "priority-low",
										"medium" => "priority-medium",
										"high" => "priority-high",
										"critical" => "priority-critical",
										_ => "priority-low"
									};
								}
								<span class="priority-badge @priorityClass">@Model.Priority</span>
							</span>
						</div>
					</div>
				</div>

				<!-- Timeline Information -->
				<div class="details-section">
					<h3 class="details-section-title">
						<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
						Timeline
					</h3>
					<div class="detail-item">
						<span class="detail-label">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
							</svg>
							Start Date
						</span>
						<span class="detail-value">@Model.StartDate</span>
					</div>
					<div class="detail-item">
						<span class="detail-label">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
							</svg>
							Deadline
						</span>
						<span class="detail-value">
							@if (!string.IsNullOrWhiteSpace(Model.Deadline))
							{
								<strong style="color: #dc2626;">@Model.Deadline</strong>
							}
							else
							{
								<span style="color: #6b7280;">No deadline set</span>
							}
						</span>
					</div>
					<div class="detail-item">
						<span class="detail-label">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
							</svg>
							End Date
						</span>
						<span class="detail-value">
							@if (!string.IsNullOrWhiteSpace(Model.EndDate))
							{
								@Model.EndDate
							}
							else
							{
								<span style="color: #6b7280;">Not completed</span>
							}
						</span>
					</div>
				</div>

				<!-- Assignment Information -->
				<div class="details-section">
					<h3 class="details-section-title">
						<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
						</svg>
						Assignment
					</h3>
					<div class="detail-item">
						<span class="detail-label">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
							</svg>
							Assigned CMM
						</span>
						<span class="detail-value">@Model.AssignedMachineName</span>
					</div>
				</div>

				<!-- Description -->
				<div class="details-section" style="grid-column: 1 / -1;">
					<h3 class="details-section-title">
						<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
						</svg>
						Description
					</h3>
					<div class="detail-item">
						<span class="detail-value" style="text-align: left; white-space: pre-wrap;">@Model.Description</span>
					</div>
				</div>

				<!-- Action Buttons -->
				@if (User.Identity?.IsAuthenticated == true && (User.IsAdmin() || User.IsOperator()))
				{
					<div class="details-actions">
						<a href="@Url.Action("All", "Task")" class="details-btn details-btn-back">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
							</svg>
							Back to Tasks
						</a>
						<a href="@Url.Action("Edit", "Task", new { id = Model.Id, extension = Model.GetTaskExtension() })" class="details-btn details-btn-edit">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
							</svg>
							Edit Task
						</a>
						<a href="@Url.Action("Delete", "Task", new { id = Model.Id, extension = Model.GetTaskExtension() })" class="details-btn details-btn-delete">
							<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
							</svg>
							Delete Task
						</a>
					</div>
				}
			</div>
		</div>
	</div>
</div>

@section Scripts {
	<script src="~/js/pages/details.js?v=@DateTime.Now.Ticks"></script>
}
