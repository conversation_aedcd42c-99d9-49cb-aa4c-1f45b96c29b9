/* Authentication Pages CSS - Modern & Beautiful Design */
/* Enhanced styling for Login and Register pages */

/* Auth Page Container */
.auth-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.auth-page .container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
}

/* Auth Card Container */
.auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
    padding: 3rem;
    width: 100%;
    max-width: 450px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

/* Auth Header */
.auth-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.auth-title {
    color: #2d3748 !important;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-subtitle {
    color: #718096 !important;
    font-size: 1rem;
    font-weight: 400;
    margin-bottom: 0;
}

/* Form Styling */
.auth-form {
    margin-bottom: 1.5rem;
}

.auth-form .form-floating {
    margin-bottom: 1.5rem;
    position: relative;
}

.auth-form .form-control {
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 1rem 1.25rem;
    font-size: 1rem;
    background: #ffffff;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.auth-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(0, 0, 0, 0.05);
    background: #ffffff;
}

.auth-form .form-control:valid {
    border-color: #48bb78;
}

.auth-form .form-floating > label {
    color: #718096;
    font-weight: 500;
    padding: 1rem 1.25rem;
}

.auth-form .form-control:focus ~ label,
.auth-form .form-control:not(:placeholder-shown) ~ label {
    color: #667eea;
    font-weight: 600;
}

/* Validation Messages - Only show when there are actual errors */
.text-danger {
    color: #e53e3e !important;
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(229, 62, 62, 0.1);
    border-left: 3px solid #e53e3e;
    padding: 0.5rem 0.75rem;
    border-radius: 0 6px 6px 0;
    transition: all 0.3s ease;
}

.text-danger::before {
    content: '⚠';
    font-size: 1rem;
}

/* Hide validation messages when they are empty */
.text-danger:empty,
.text-danger:not(:has(text)),
span[data-valmsg-for]:empty,
span[data-valmsg-for]:not(:has(text)) {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Show validation messages only when they have content */
.text-danger:not(:empty),
span[data-valmsg-for]:not(:empty) {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
}

/* Hide validation summary when empty */
div[asp-validation-summary]:empty,
.validation-summary-errors:empty,
.validation-summary-valid {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Show validation summary only when it has errors */
.validation-summary-errors:not(:empty) {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    background: rgba(229, 62, 62, 0.1);
    border: 1px solid rgba(229, 62, 62, 0.3);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.validation-summary-errors ul {
    margin: 0;
    padding-left: 1.5rem;
}

.validation-summary-errors li {
    color: #e53e3e;
    font-weight: 500;
}

/* Checkbox Styling */
.auth-checkbox {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 1.5rem 0;
    padding: 0.75rem;
    background: #f7fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.auth-checkbox .form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid #cbd5e0;
    border-radius: 4px;
    margin: 0;
}

.auth-checkbox .form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.auth-checkbox .form-label {
    color: #4a5568 !important;
    font-weight: 500;
    margin: 0;
    cursor: pointer;
}

/* Submit Button */
.auth-submit-btn {
    width: 100%;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border: none;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.auth-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.auth-submit-btn:active {
    transform: translateY(0);
}

.auth-submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.auth-submit-btn:hover::before {
    left: 100%;
}

/* Links */
.auth-link {
    color: #667eea !important;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.auth-link:hover {
    color: #5a67d8 !important;
    text-decoration: none;
    transform: translateX(2px);
}

.auth-links {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e2e8f0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-card {
        padding: 2rem 1.5rem;
        margin: 1rem;
        border-radius: 16px;
    }
    
    .auth-title {
        font-size: 1.75rem;
    }
    
    .auth-form .form-control {
        padding: 0.875rem 1rem;
    }
    
    .auth-form .form-floating > label {
        padding: 0.875rem 1rem;
    }
}

@media (max-width: 480px) {
    .auth-page .container {
        padding: 1rem 0.5rem;
    }
    
    .auth-card {
        padding: 1.5rem 1rem;
        margin: 0.5rem;
    }
    
    .auth-title {
        font-size: 1.5rem;
    }
}

/* Loading State */
.auth-submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.auth-submit-btn:disabled:hover {
    transform: none;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Success State */
.form-control.is-valid {
    border-color: #48bb78;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2348bb78' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l.94.94L4.66 9.2z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 1rem;
}

/* Error State */
.form-control.is-invalid {
    border-color: #e53e3e;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23e53e3e'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4M8.2 4.6l-2.4 2.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 1rem;
}

/* Additional Visual Enhancements */

/* Floating Animation for Auth Card */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.auth-card {
    animation: float 6s ease-in-out infinite;
}

/* Gradient Text Animation */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.auth-title {
    background: linear-gradient(-45deg, #667eea, #764ba2, #667eea, #5a67d8);
    background-size: 400% 400%;
    animation: gradientShift 4s ease infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Input Focus Glow Effect */
.auth-form .form-control:focus {
    position: relative;
}

.auth-form .form-control:focus::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 14px;
    z-index: -1;
    opacity: 0.3;
    filter: blur(8px);
}

/* Button Pulse Effect */
@keyframes pulse {
    0% { box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3); }
    50% { box-shadow: 0 4px 20px rgba(102, 126, 234, 0.5); }
    100% { box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3); }
}

.auth-submit-btn:not(:disabled) {
    animation: pulse 2s infinite;
}

/* Background Particles Effect */
.auth-page::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Duplicate validation styling removed - handled above */

/* Enhanced Link Hover Effects */
.auth-link {
    position: relative;
    overflow: hidden;
}

.auth-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.3s ease;
}

.auth-link:hover::after {
    width: 100%;
}

/* Form Field Icons */
.form-floating {
    position: relative;
}

.form-floating input[type="email"] + label::before {
    content: '📧';
    margin-right: 0.5rem;
}

.form-floating input[type="password"] + label::before {
    content: '🔒';
    margin-right: 0.5rem;
}

/* Success Animation */
@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.form-control.is-valid {
    animation: successPulse 0.5s ease;
}

/* Mobile Optimizations */
@media (max-width: 480px) {
    .auth-card {
        animation: none; /* Disable floating on mobile for better performance */
    }

    .auth-title {
        animation-duration: 6s; /* Slower animation on mobile */
    }
}
