/* Reusable Card Components */
/* Modern card designs that can be used across different pages */

/* Base Card Styles */
.card-modern {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    overflow: hidden;
    border: 1px solid #e5e7eb;
    position: relative;
}

.card-modern:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
}

/* Card Header */
.card-header-modern {
    padding: 1.5rem 1.5rem 1rem;
    border-bottom: 1px solid #f3f4f6;
}

.card-title-modern {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.card-subtitle-modern {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 0;
}

/* Card Body */
.card-body-modern {
    padding: 1rem 1.5rem;
}

/* Card Footer */
.card-footer-modern {
    padding: 1rem 1.5rem;
    background: #f9fafb;
    border-top: 1px solid #f3f4f6;
}

/* Status Badges */
.status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge-success {
    background: #dcfce7;
    color: #166534;
}

.status-badge-warning {
    background: #fef3c7;
    color: #92400e;
}

.status-badge-info {
    background: #dbeafe;
    color: #1e40af;
}

.status-badge-danger {
    background: #fee2e2;
    color: #991b1b;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.action-btn-modern {
    flex: 1;
    min-width: 80px;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 600;
    font-size: 0.875rem;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.action-btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.action-btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-1px);
    color: white;
}

.action-btn-secondary {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.action-btn-secondary:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-1px);
    color: white;
}

.action-btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.action-btn-danger:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-1px);
    color: white;
}

/* Info Items */
.info-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-label {
    font-weight: 600;
    color: #374151;
    min-width: 80px;
    font-size: 0.875rem;
}

.info-value {
    color: #6b7280;
    flex: 1;
    font-size: 0.875rem;
}

/* Grid Layouts */
.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.cards-grid-large {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 2rem;
}

.cards-grid-small {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .cards-grid,
    .cards-grid-large,
    .cards-grid-small {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .card-header-modern,
    .card-body-modern,
    .card-footer-modern {
        padding: 1rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .action-btn-modern {
        min-width: auto;
    }
}

/* Loading States */
.card-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.card-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2rem;
    height: 2rem;
    margin: -1rem 0 0 -1rem;
    border: 3px solid #e5e7eb;
    border-top-color: #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Animations */
@keyframes spin {
    to { transform: rotate(360deg); }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Accessibility */
.card-modern:focus-within {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .card-modern {
        border: 2px solid #000;
    }
    
    .action-btn-modern {
        border: 2px solid currentColor;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .card-modern,
    .action-btn-modern {
        transition: none;
    }
    
    .card-modern:hover {
        transform: none;
    }
    
    .action-btn-modern:hover {
        transform: none;
    }
    
    .fade-in-up {
        animation: none;
    }
}
