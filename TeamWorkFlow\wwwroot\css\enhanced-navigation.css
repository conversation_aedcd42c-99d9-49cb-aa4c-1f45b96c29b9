/* Enhanced Navigation Styling */
/* Modern, professional navigation design without brand logo */

/* Base Navigation Styles */
.navbar {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%) !important;
    border-bottom: none !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    padding: 0.75rem 1.5rem !important;
    min-height: 70px !important;
    position: relative !important;
    z-index: 1000 !important;
    backdrop-filter: blur(10px) !important;
}

.navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.95) 0%, rgba(59, 130, 246, 0.95) 50%, rgba(96, 165, 250, 0.95) 100%);
    z-index: -1;
}

/* Container adjustments without brand */
.navbar .container-fluid {
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
}

/* Navigation collapse adjustments */
.navbar-collapse {
    flex-grow: 1 !important;
    justify-content: space-between !important;
}

/* Desktop navigation menu styling */
@media (min-width: 992px) {
    .navbar-nav.flex-grow-1 {
        flex-direction: row !important;
        align-items: center !important;
        gap: 0.75rem !important;
        margin-right: auto !important;
    }
}

/* Mobile navigation menu styling */
@media (max-width: 991.98px) {
    .navbar-collapse {
        position: absolute !important;
        top: 100% !important;
        left: 0 !important;
        right: 0 !important;
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%) !important;
        border-radius: 0 0 0.5rem 0.5rem !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
        padding: 1rem !important;
        margin-top: 0.5rem !important;
        display: none !important;
    }

    .navbar-collapse.show {
        display: block !important;
    }

    .navbar-nav.flex-grow-1 {
        flex-direction: column !important;
        align-items: stretch !important;
        gap: 0.5rem !important;
        width: 100% !important;
    }

    .navbar-nav .nav-item {
        width: 100% !important;
    }

    .navbar-nav .nav-link {
        text-align: center !important;
        padding: 0.75rem 1rem !important;
        border-radius: 0.375rem !important;
        margin-bottom: 0.25rem !important;
    }
}

/* Enhanced navigation buttons */
.navbar-nav .nav-item {
    margin: 0 !important;
}

.navbar-nav .nav-link {
    display: inline-flex !important;
    align-items: center !important;
    padding: 0.625rem 1.25rem !important;
    border-radius: 0.5rem !important;
    font-size: 0.95rem !important;
    font-weight: 700 !important;
    text-decoration: none !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    white-space: nowrap !important;
    border: 2px solid transparent !important;
    position: relative !important;
    overflow: hidden !important;
    letter-spacing: 0.025em !important;
}

/* Main navigation button styles - Enhanced visibility */
.custom-button-color {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
    color: #1e40af !important;
    border: 2px solid #ffffff !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    text-shadow: none !important;
    font-weight: 700 !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
}

.custom-button-color::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(30, 64, 175, 0.1), transparent);
    transition: left 0.5s ease;
    z-index: 0;
}

.custom-button-color:hover::before {
    left: 100%;
}

/* Enhanced hover state for better visibility */
.custom-button-color:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
    color: #1e40af !important;
    border-color: #e2e8f0 !important;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3), 0 3px 6px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-1px) !important;
}

/* Focus state for accessibility */
.custom-button-color:focus {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
    color: #1e40af !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), 0 4px 12px rgba(0, 0, 0, 0.25) !important;
    outline: none !important;
}

/* Duplicate hover rule removed - using the one above */

.custom-button-color:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* Admin "For Check" button styling - Enhanced visibility */
.custom-button-color-admin {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%) !important;
    color: white !important;
    border: 2px solid #ffffff !important;
    box-shadow: 0 4px 12px rgba(14, 165, 233, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    font-weight: 700 !important;
    transition: all 0.3s ease !important;
}

.custom-button-color-admin:hover {
    background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%) !important;
    color: white !important;
    border-color: #ffffff !important;
    box-shadow: 0 6px 16px rgba(14, 165, 233, 0.5), 0 3px 6px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-1px) !important;
}

.custom-button-color-red {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
    color: white !important;
    border: 2px solid #ffffff !important;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    font-weight: 700 !important;
    transition: all 0.3s ease !important;
}

.custom-button-color-red:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%) !important;
    color: white !important;
    border-color: #ffffff !important;
    box-shadow: 0 6px 16px rgba(239, 68, 68, 0.5), 0 3px 6px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-1px) !important;
}

.custom-button-color-admin:hover {
    background: linear-gradient(135deg, #38bdf8 0%, #0ea5e9 100%) !important;
    border-color: rgba(255, 255, 255, 0.7) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(14, 165, 233, 0.5) !important;
}

.custom-button-color-red:hover {
    background: linear-gradient(135deg, #f87171 0%, #ef4444 100%) !important;
    border-color: rgba(255, 255, 255, 0.7) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.5) !important;
    text-decoration: none !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
    font-weight: 700 !important;
}

/* User section styling */
.navbar-nav:last-child {
    flex-direction: row !important;
    align-items: center !important;
    gap: 0.5rem !important;
    margin-left: auto !important;
}

.navbar-nav:last-child .nav-item {
    margin: 0 !important;
}

.navbar-nav:last-child .nav-link {
    color: white !important;
    background: rgba(255, 255, 255, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    padding: 0.5rem 1rem !important;
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    border-radius: 0.375rem !important;
    transition: all 0.3s ease !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.navbar-nav:last-child .nav-link:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.25) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    transform: translateY(-1px) !important;
    text-decoration: none !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Logout button styling */
.navbar-nav:last-child form button {
    background: rgba(239, 68, 68, 0.9) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    padding: 0.5rem 1rem !important;
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    border-radius: 0.375rem !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.navbar-nav:last-child form button:hover {
    background: rgba(239, 68, 68, 1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 6px 15px rgba(239, 68, 68, 0.4) !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
}

/* First navbar-toggler definition removed - using the one below */

/* Responsive Design */
@media (max-width: 991px) {
    .navbar {
        padding: 0.5rem 1rem !important;
        min-height: 60px !important;
    }
    
    .navbar-collapse {
        margin-top: 1rem !important;
        padding-top: 1rem !important;
        border-top: 1px solid rgba(255, 255, 255, 0.2) !important;
    }
    
    .navbar-nav.flex-grow-1 {
        flex-direction: column !important;
        gap: 0.5rem !important;
        width: 100% !important;
        margin-bottom: 1rem !important;
    }
    
    .navbar-nav:last-child {
        flex-direction: column !important;
        gap: 0.5rem !important;
        width: 100% !important;
    }
    
    .navbar-nav .nav-link,
    .navbar-nav:last-child .nav-link,
    .navbar-nav:last-child form button {
        width: 100% !important;
        text-align: center !important;
        padding: 12px 16px !important;
        font-size: 16px !important;
        font-weight: 700 !important;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
        color: #1e40af !important;
        border: 2px solid #ffffff !important;
        border-radius: 8px !important;
        margin: 4px 0 !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        transition: all 0.3s ease !important;
    }
}

@media (max-width: 768px) {
    .navbar {
        padding: 0.375rem 0.75rem !important;
        min-height: 56px !important;
    }
    
    .navbar-nav .nav-link {
        font-size: 0.875rem !important;
        padding: 0.625rem 1rem !important;
    }
}

@media (max-width: 576px) {
    .navbar {
        padding: 0.25rem 0.5rem !important;
        min-height: 52px !important;
    }
    
    .navbar-nav .nav-link {
        font-size: 0.8rem !important;
        padding: 0.5rem 0.875rem !important;
    }
}

/* Animation for navigation items */
@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.navbar-nav .nav-item {
    animation: slideInFromTop 0.6s ease-out;
}

.navbar-nav .nav-item:nth-child(1) { animation-delay: 0.1s; }
.navbar-nav .nav-item:nth-child(2) { animation-delay: 0.2s; }
.navbar-nav .nav-item:nth-child(3) { animation-delay: 0.3s; }
.navbar-nav .nav-item:nth-child(4) { animation-delay: 0.4s; }
.navbar-nav .nav-item:nth-child(5) { animation-delay: 0.5s; }
.navbar-nav .nav-item:nth-child(6) { animation-delay: 0.6s; }

/* Enhanced navbar toggler for better visibility */
.navbar-toggler {
    border: 2px solid #ffffff !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
    backdrop-filter: blur(10px) !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25) !important;
    transition: all 0.3s ease !important;
}

.navbar-toggler:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3) !important;
    transform: translateY(-1px) !important;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2830, 64, 175, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='3' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
    width: 24px !important;
    height: 24px !important;
}

/* BACK TO HOME BUTTON STYLING - For Login/Register Pages */
/* Ensure Back to Home button is always visible and properly styled on all device sizes */
.navbar-nav .nav-link[href*="Home"] {
    color: #1e40af !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
    border: 2px solid #ffffff !important;
    font-weight: 700 !important;
    text-shadow: none !important;
    opacity: 1 !important;
    visibility: visible !important;
    text-decoration: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    margin: 4px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 140px !important;
}

.navbar-nav .nav-link[href*="Home"]:hover {
    color: #1e40af !important;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
    border-color: #e2e8f0 !important;
    text-decoration: none !important;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3), 0 3px 6px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-1px) !important;
}

/* Ensure Back to Home button is always visible on all screen sizes */
@media (max-width: 991.98px) {
    .navbar-nav .nav-link[href*="Home"] {
        width: 100% !important;
        text-align: center !important;
        margin: 8px 0 !important;
        padding: 12px 20px !important;
    }
}

@media (min-width: 992px) {
    .navbar-nav .nav-link[href*="Home"] {
        margin-left: 0 !important;
        margin-right: auto !important;
    }
}

/* LOGIN/REGISTER PAGES - NAVIGATION BEHAVIOR */
/* Ensure navbar-collapse is always visible on login/register pages (no hamburger menu) */
body[data-page="auth"] .navbar-collapse,
.auth-page .navbar-collapse {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: static !important;
    background: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
}

body[data-page="auth"] .navbar-nav,
.auth-page .navbar-nav {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: flex-start !important;
    width: auto !important;
    margin: 0 !important;
}

/* Ensure Back to Home button is prominent on auth pages */
body[data-page="auth"] .navbar-nav .nav-link[href*="Home"],
.auth-page .navbar-nav .nav-link[href*="Home"] {
    margin-right: auto !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
}

/* Mobile behavior for auth pages - still show Back to Home prominently */
@media (max-width: 991.98px) {
    body[data-page="auth"] .navbar-collapse,
    .auth-page .navbar-collapse {
        display: flex !important;
        flex-direction: column !important;
        align-items: stretch !important;
        padding: 1rem 0 !important;
    }

    body[data-page="auth"] .navbar-nav,
    .auth-page .navbar-nav {
        flex-direction: column !important;
        width: 100% !important;
    }
}

/* Hide Login/Register buttons on auth pages (they're redundant) */
.auth-page .navbar-nav .nav-link[href*="Login"],
.auth-page .navbar-nav .nav-link[href*="Register"] {
    display: none !important;
}

/* Style all navigation links properly */
.navbar-nav .nav-link {
    color: #1e40af !important;
    font-weight: 600 !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
}

.navbar-nav .nav-link:hover {
    color: #1e40af !important;
    text-decoration: none !important;
    opacity: 0.8 !important;
}

/* Ensure logout button is properly styled */
.navbar-nav .nav-link.btn.btn-link {
    color: #1e40af !important;
    background: none !important;
    border: none !important;
    padding: 0.5rem 1rem !important;
    font-weight: 600 !important;
}

.navbar-nav .nav-link.btn.btn-link:hover {
    color: #1e40af !important;
    background: rgba(30, 64, 175, 0.1) !important;
    text-decoration: none !important;
}

/* Focus states for accessibility */
.navbar-nav .nav-link:focus,
.navbar-nav:last-child .nav-link:focus,
.navbar-nav:last-child form button:focus,
.navbar-toggler:focus {
    outline: 3px solid rgba(59, 130, 246, 0.5) !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3) !important;
}

/* ADMIN DASHBOARD - ENSURE NAVIGATION IS ALWAYS VISIBLE */
.admin-nav .navbar-collapse {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Login/Register page specific navigation styling */
.navbar .nav-link.text-white {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
    color: #1e40af !important;
    border: 2px solid #ffffff !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    margin: 0 4px !important;
    font-weight: 700 !important;
    text-decoration: none !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.navbar .nav-link.text-white:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
    color: #1e40af !important;
    border-color: #e2e8f0 !important;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3), 0 3px 6px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-1px) !important;
    text-decoration: none !important;
}

/* Ensure all navigation text is highly visible */
.navbar-nav .nav-link {
    font-weight: 700 !important;
    text-shadow: none !important;
    letter-spacing: 0.025em !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .navbar {
        background: #000080 !important;
        border-bottom: 2px solid #ffffff !important;
    }

    .custom-button-color,
    .navbar .nav-link.text-white {
        background: #ffffff !important;
        color: #000080 !important;
        border-color: #ffffff !important;
    }

    .custom-button-color:hover,
    .navbar .nav-link.text-white:hover {
        background: #f0f0f0 !important;
        color: #000080 !important;
    }
}

/* SPRINT TO DO BUTTON SPECIFIC FIX */
/* Maximum specificity to override all other styles */
body .navbar .navbar-nav .nav-link.btn.custom-button-color[href*="Sprint"] {
    color: #1e40af !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
    border: 2px solid #ffffff !important;
    font-weight: 700 !important;
    text-shadow: none !important;
    opacity: 1 !important;
    visibility: visible !important;
    text-decoration: none !important;
}

body .navbar .navbar-nav .nav-link.btn.custom-button-color[href*="Sprint"]:hover {
    color: #1e40af !important;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
    border-color: #e2e8f0 !important;
    text-decoration: none !important;
}

/* PARTS BUTTON SPECIFIC FIX */
body .navbar .navbar-nav .nav-link.btn.custom-button-color[href*="Part"] {
    color: #1e40af !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
    border: 2px solid #ffffff !important;
    font-weight: 700 !important;
    text-shadow: none !important;
    opacity: 1 !important;
    visibility: visible !important;
    text-decoration: none !important;
}

body .navbar .navbar-nav .nav-link.btn.custom-button-color[href*="Part"]:hover {
    color: #1e40af !important;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
    border-color: #e2e8f0 !important;
    text-decoration: none !important;
}

/* REGISTER BUTTON SPECIFIC FIX */
body .navbar .navbar-nav .nav-link.btn.custom-button-color[href*="Register"] {
    color: #1e40af !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
    border: 2px solid #ffffff !important;
    font-weight: 700 !important;
    text-shadow: none !important;
    opacity: 1 !important;
    visibility: visible !important;
    text-decoration: none !important;
}

body .navbar .navbar-nav .nav-link.btn.custom-button-color[href*="Register"]:hover {
    color: #1e40af !important;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
    border-color: #e2e8f0 !important;
    text-decoration: none !important;
}

/* LOGIN BUTTON SPECIFIC FIX */
body .navbar .navbar-nav .nav-link[href*="Login"] {
    color: #1e40af !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
    border: 2px solid #ffffff !important;
    font-weight: 700 !important;
    text-shadow: none !important;
    opacity: 1 !important;
    visibility: visible !important;
    text-decoration: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    margin: 0 4px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

body .navbar .navbar-nav .nav-link[href*="Login"]:hover {
    color: #1e40af !important;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
    border-color: #e2e8f0 !important;
    text-decoration: none !important;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3), 0 3px 6px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-1px) !important;
}
