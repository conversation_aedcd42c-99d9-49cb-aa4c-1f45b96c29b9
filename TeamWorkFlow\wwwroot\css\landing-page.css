/* Landing Page Styles - Modern, Professional Design */

/* Reset and Base Styles */
* {
    box-sizing: border-box;
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding: 2rem 0;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>') no-repeat center center;
    background-size: cover;
    opacity: 0.3;
}

.hero-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-content {
    color: white;
}

.hero-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50px;
    padding: 0.5rem 1.5rem;
    margin-bottom: 2rem;
    animation: fadeInUp 0.8s ease-out;
}

.badge-text {
    font-size: 0.9rem;
    font-weight: 500;
    color: white;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.hero-title-gradient {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    max-width: 600px;
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    animation: fadeInUp 0.8s ease-out 0.6s both;
}

.btn-primary-hero {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 1rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.btn-primary-hero:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.6);
    color: white;
    text-decoration: none;
}

.btn-secondary-hero {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 1rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-secondary-hero:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.btn-icon {
    width: 20px;
    height: 20px;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    animation: fadeInUp 0.8s ease-out 0.8s both;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #ffd700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 500;
}

.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image-container {
    position: relative;
    max-width: 500px;
    width: 100%;
    animation: fadeInRight 1s ease-out 0.4s both;
}

.hero-image {
    width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.hero-image:hover {
    transform: scale(1.05);
}

.hero-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    border-radius: 20px;
    pointer-events: none;
}

/* Features Section */
.features-section {
    padding: 6rem 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.features-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.features-header {
    text-align: center;
    margin-bottom: 4rem;
}

.features-title {
    font-size: 3rem;
    font-weight: 800;
    color: #1a202c;
    margin-bottom: 1rem;
}

.features-subtitle {
    font-size: 1.25rem;
    color: #4a5568;
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2.5rem;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    opacity: 0;
    transform: translateY(30px);
}

.feature-card.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: white;
}

.feature-icon svg {
    width: 30px;
    height: 30px;
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 1rem;
}

.feature-description {
    color: #4a5568;
    line-height: 1.6;
    font-size: 1rem;
}

/* Benefits Section */
.benefits-section {
    padding: 6rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.benefits-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.benefits-content {
    margin-bottom: 4rem;
}

.benefits-title {
    font-size: 3rem;
    font-weight: 800;
    text-align: center;
    margin-bottom: 1rem;
}

.benefits-subtitle {
    font-size: 1.25rem;
    text-align: center;
    opacity: 0.9;
    margin-bottom: 3rem;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.benefit-item {
    text-align: center;
    padding: 2rem;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.3s ease;
}

.benefit-item.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.benefit-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.benefit-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.benefit-description {
    opacity: 0.9;
    line-height: 1.6;
}

.benefits-cta {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 3rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.cta-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-description {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.btn-cta {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 1.25rem 2.5rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.btn-cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.6);
    color: white;
    text-decoration: none;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .hero-container {
        gap: 3rem;
    }

    .hero-title {
        font-size: 3.5rem;
    }

    .features-title,
    .benefits-title {
        font-size: 2.5rem;
    }
}

@media (max-width: 992px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .hero-title {
        font-size: 3rem;
    }

    .hero-description {
        margin-left: auto;
        margin-right: auto;
    }

    .hero-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .benefits-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .hero-section {
        padding: 1rem 0;
        min-height: auto;
    }

    .hero-container {
        padding: 0 1rem;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 1.1rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn-primary-hero,
    .btn-secondary-hero {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .hero-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .features-section,
    .benefits-section {
        padding: 4rem 0;
    }

    .features-container,
    .benefits-container {
        padding: 0 1rem;
    }

    .features-title,
    .benefits-title {
        font-size: 2rem;
    }

    .features-subtitle,
    .benefits-subtitle {
        font-size: 1.1rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .feature-card {
        padding: 2rem;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .benefits-cta {
        padding: 2rem;
    }

    .cta-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .btn-primary-hero,
    .btn-secondary-hero,
    .btn-cta {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .features-title,
    .benefits-title {
        font-size: 1.75rem;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
    }

    .feature-icon svg {
        width: 25px;
        height: 25px;
    }

    .benefit-icon {
        font-size: 2.5rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .features-section {
        background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    }

    .feature-card {
        background: #2d3748;
        border-color: rgba(255, 255, 255, 0.1);
    }

    .feature-title {
        color: #f7fafc;
    }

    .feature-description {
        color: #cbd5e0;
    }

    .features-title {
        color: #f7fafc;
    }

    .features-subtitle {
        color: #cbd5e0;
    }
}

/* Enhanced Navigation for Landing Page */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98) !important;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 800;
    font-size: 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar .nav-link {
    color: #4a5568 !important;
    font-weight: 600;
    padding: 0.75rem 1rem !important;
    border-radius: 8px;
    transition: all 0.3s ease;
    margin: 0 0.25rem;
}

.navbar .nav-link:hover {
    color: #667eea !important;
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.navbar .nav-link svg {
    transition: all 0.3s ease;
}

.navbar .nav-link:hover svg {
    transform: scale(1.1);
}

/* Enhanced Login/Register Buttons */
.navbar .nav-item:last-child .nav-link,
.navbar .nav-item:nth-last-child(2) .nav-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white !important;
    border-radius: 25px;
    padding: 0.75rem 1.5rem !important;
    margin: 0 0.25rem;
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

.navbar .nav-item:last-child .nav-link:hover,
.navbar .nav-item:nth-last-child(2) .nav-link:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.navbar .nav-item:nth-last-child(2) .nav-link {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea !important;
    box-shadow: none;
}

.navbar .nav-item:nth-last-child(2) .nav-link:hover {
    background: #667eea;
    color: white !important;
}

/* Mobile Navigation Improvements */
@media (max-width: 991px) {
    .navbar-collapse {
        background: white;
        border-radius: 12px;
        margin-top: 1rem;
        padding: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .navbar .nav-link {
        margin: 0.25rem 0;
        text-align: center;
    }

    .navbar .nav-item:last-child .nav-link,
    .navbar .nav-item:nth-last-child(2) .nav-link {
        margin: 0.5rem 0;
        text-align: center;
    }
}

/* Smooth Scroll Behavior */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease;
    /* CSS-only fallback: auto-hide after 3 seconds */
    animation: autoHideLoading 3s ease-in-out forwards;
}

.loading-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

/* CSS-only auto-hide animation */
@keyframes autoHideLoading {
    0%, 80% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        pointer-events: none;
    }
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
    40%, 43% { transform: translateY(-10px); }
    70% { transform: translateY(-5px); }
    90% { transform: translateY(-2px); }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Enhanced Animation Classes */
.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-bounce {
    animation: bounce 2s infinite;
}

.animate-slide-in-left {
    animation: slideInLeft 0.8s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.8s ease-out;
}

.animate-zoom-in {
    animation: zoomIn 0.6s ease-out;
}

/* Hover Animations */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.hover-glow {
    transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
}

/* Staggered Animation Delays */
.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }
.feature-card:nth-child(5) { animation-delay: 0.5s; }
.feature-card:nth-child(6) { animation-delay: 0.6s; }

.benefit-item:nth-child(1) { animation-delay: 0.1s; }
.benefit-item:nth-child(2) { animation-delay: 0.2s; }
.benefit-item:nth-child(3) { animation-delay: 0.3s; }
.benefit-item:nth-child(4) { animation-delay: 0.4s; }

/* Floating Animation for Hero Elements */
.hero-badge {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Gradient Animation */
.gradient-animate {
    background-size: 200% 200%;
    animation: gradientShift 4s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Print styles */
@media print {
    .hero-section,
    .features-section,
    .benefits-section {
        background: white !important;
        color: black !important;
    }

    .hero-actions,
    .benefits-cta,
    .navbar {
        display: none !important;
    }
}
