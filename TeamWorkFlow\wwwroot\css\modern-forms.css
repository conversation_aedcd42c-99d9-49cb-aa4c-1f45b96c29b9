/* Modern Forms CSS */
/* Beautiful, responsive form styling for all edit and add pages */

/* Form Container */
.modern-form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(20px);
    border-radius: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
}

.modern-form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
}

/* Form Header */
.form-header {
    text-align: center;
    margin-bottom: 2.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.form-title {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.form-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.125rem;
    font-weight: 400;
    margin: 0;
}

/* Form Groups */
.modern-form-group {
    margin-bottom: 2rem;
    position: relative;
}

.modern-form-group:last-of-type {
    margin-bottom: 0;
}

/* Form Labels */
.modern-form-label {
    display: block;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.75rem;
    position: relative;
    padding-left: 1.5rem;
}

.modern-form-label::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
    border-radius: 2px;
}

/* Form Inputs */
.modern-form-input,
.modern-form-select,
.modern-form-textarea {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    color: white;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    position: relative;
}

.modern-form-input::placeholder,
.modern-form-textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
    font-weight: 400;
}

.modern-form-input:focus,
.modern-form-select:focus,
.modern-form-textarea:focus {
    outline: none;
    border-color: rgba(139, 92, 246, 0.8);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.2), 0 8px 25px -5px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.modern-form-input:hover,
.modern-form-select:hover,
.modern-form-textarea:hover {
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.12);
}

/* Select Styling */
.modern-form-select {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='rgba(255,255,255,0.8)' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 3rem;
    appearance: none;
}

.modern-form-select option {
    background: #374151;
    color: white;
    padding: 0.5rem;
}

/* Textarea */
.modern-form-textarea {
    resize: vertical;
    min-height: 120px;
    line-height: 1.6;
}

/* Validation Messages */
.modern-validation-message {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 0.25rem;
    padding: 0.375rem 0.5rem;
    display: block;
    animation: fadeIn 0.2s ease;
}

/* Success validation message */
.modern-validation-success {
    color: rgba(16, 185, 129, 0.9);
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 400;
    margin-top: 0.25rem;
    padding: 0.375rem 0.5rem;
    display: block;
    animation: fadeIn 0.2s ease;
}

/* ASP.NET Core server-side validation messages */
.field-validation-error,
.text-danger,
.validation-summary-errors {
    color: #ef4444 !important;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 0.25rem;
    padding: 0.375rem 0.5rem;
    display: block;
}

.field-validation-valid {
    display: none;
}



/* Form Actions */
.modern-form-actions {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Submit Button */
.modern-submit-btn {
    background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
    color: white;
    border: none;
    padding: 1rem 2.5rem;
    border-radius: 0.75rem;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 15px -3px rgba(139, 92, 246, 0.3);
    position: relative;
    overflow: hidden;
    min-width: 200px;
}

.modern-submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.modern-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(139, 92, 246, 0.4);
}

.modern-submit-btn:hover::before {
    left: 100%;
}

.modern-submit-btn:active {
    transform: translateY(0);
}

/* Cancel Button */
.modern-cancel-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 150px;
}

.modern-cancel-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

/* Form Grid for Multiple Columns */
.modern-form-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
}

@media (min-width: 768px) {
    .modern-form-grid.two-columns {
        grid-template-columns: 1fr 1fr;
    }
}

/* Input Icons */
.modern-form-group.has-icon {
    position: relative;
}

.modern-form-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1.25rem;
    height: 1.25rem;
    color: rgba(255, 255, 255, 0.6);
    z-index: 1;
}

.modern-form-group.has-icon .modern-form-input,
.modern-form-group.has-icon .modern-form-select {
    padding-left: 3rem;
}

/* Loading State */
.modern-form-loading {
    opacity: 0.7;
    pointer-events: none;
}

.modern-submit-btn.loading {
    background: #6b7280;
    cursor: not-allowed;
}

.modern-submit-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success State */
.modern-form-success {
    border-color: #10b981;
    background: rgba(16, 185, 129, 0.1);
}

.modern-form-success:focus {
    border-color: #10b981;
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.2);
}

/* Error State - Keep clean, no red styling */
.modern-form-error {
    /* No visual changes to the field itself */
}

.modern-form-error:focus {
    /* Keep normal focus styling */
    border-color: #8b5cf6;
    box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.2);
}

/* Date Format Hint */
.date-format-hint {
    font-size: 0.75rem !important;
    color: rgba(255, 255, 255, 0.7) !important;
    font-weight: 500 !important;
    margin-left: 0.5rem;
    background: rgba(139, 92, 246, 0.2);
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    border: 1px solid rgba(139, 92, 246, 0.3);
}

/* Custom Date Input Styling (converted from HTML5 date inputs) */
.modern-form-input[type="text"][placeholder="dd/MM/yyyy"] {
    position: relative;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='rgba(255,255,255,0.8)'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 1.5rem 1.5rem;
    padding-right: 3.5rem !important;
}

.modern-form-input[type="text"][placeholder="dd/MM/yyyy"]:hover {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='rgba(255,255,255,1)'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'/%3e%3c/svg%3e");
}

.modern-form-input[type="text"][placeholder="dd/MM/yyyy"]:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='rgba(139,92,246,0.9)'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'/%3e%3c/svg%3e");
}

/* HTML5 Date Input Styling (fallback) */
.modern-form-input[type="date"] {
    position: relative;
    color-scheme: dark;
}

.modern-form-input[type="date"]::-webkit-calendar-picker-indicator {
    background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='rgba(255,255,255,0.8)'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'/%3e%3c/svg%3e") no-repeat;
    background-size: 1.5rem 1.5rem;
    width: 1.5rem;
    height: 1.5rem;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.3s ease;
    margin-right: 0.5rem;
}

.modern-form-input[type="date"]::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
}

/* Firefox date input styling */
.modern-form-input[type="date"]::-moz-focus-inner {
    border: 0;
}

/* Date display overlay styling */
.date-display-overlay {
    background: transparent !important;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    pointer-events: none !important;
}

.date-display-overlay:focus {
    background: transparent !important;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
}

/* Enhanced placeholder styling for custom date inputs */
.custom-date-input::placeholder {
    color: rgba(255, 255, 255, 0.7) !important;
    font-weight: 500 !important;
    font-size: 0.9rem !important;
}

.custom-date-input:focus::placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modern-form-container {
        margin: 1rem;
        padding: 1.5rem;
        border-radius: 1rem;
    }
    
    .form-title {
        font-size: 2rem;
    }
    
    .form-subtitle {
        font-size: 1rem;
    }
    
    .modern-form-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .modern-submit-btn,
    .modern-cancel-btn {
        width: 100%;
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .modern-form-container {
        margin: 0.5rem;
        padding: 1rem;
    }
    
    .form-title {
        font-size: 1.75rem;
    }
    
    .modern-form-input,
    .modern-form-select,
    .modern-form-textarea {
        padding: 0.875rem 1rem;
        font-size: 0.9rem;
    }
}

/* Validation Animations */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 100px;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
