/* Navigation Fix CSS */
/* Ensures navigation elements are always visible and properly styled */

/* Force display of navigation elements */
.navbar {
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 0.5rem 1rem !important;
    position: relative !important;
    z-index: 1000 !important;
}

/* Desktop navbar-collapse behavior */
@media (min-width: 992px) {
    .navbar-collapse {
        display: flex !important;
        flex-basis: 100% !important;
        flex-grow: 1 !important;
        align-items: center !important;
        justify-content: space-between !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .navbar-collapse.collapse {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
}

.navbar-nav {
    display: flex !important;
    flex-direction: row !important;
    padding-left: 0 !important;
    margin-bottom: 0 !important;
    list-style: none !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.navbar-nav .nav-item {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.navbar-nav .nav-link {
    display: block !important;
    padding: 0.5rem 1rem !important;
    color: rgba(0,0,0,.55) !important;
    text-decoration: none !important;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus {
    color: rgba(0,0,0,.7) !important;
}

/* Ensure login/register section is visible */
.navbar-nav:last-child {
    margin-left: auto !important;
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Override Bootstrap collapse behavior on desktop */
@media (min-width: 576px) {
    .navbar-expand-sm .navbar-collapse {
        display: flex !important;
        flex-basis: auto !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .navbar-expand-sm .navbar-nav {
        display: flex !important;
        flex-direction: row !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .navbar-expand-sm .navbar-nav .nav-link {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
}

/* Button styling for navigation */
.navbar .btn {
    display: inline-block !important;
    font-weight: 400 !important;
    line-height: 1.5 !important;
    text-align: center !important;
    text-decoration: none !important;
    vertical-align: middle !important;
    cursor: pointer !important;
    border: 1px solid transparent !important;
    padding: 0.375rem 0.75rem !important;
    font-size: 1rem !important;
    border-radius: 0.25rem !important;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out !important;
}

/* Mobile responsive */
@media (max-width: 575.98px) {
    .navbar-collapse {
        flex-direction: column !important;
        align-items: flex-start !important;
    }
    
    .navbar-nav {
        flex-direction: column !important;
        width: 100% !important;
    }
    
    .navbar-nav:last-child {
        margin-left: 0 !important;
        margin-top: 1rem !important;
    }
}

/* Ensure visibility over Tailwind and any other frameworks */
.navbar * {
    box-sizing: border-box !important;
}

/* Override any hidden/invisible classes - DESKTOP ONLY */
@media (min-width: 992px) {
    .navbar .navbar-collapse,
    .navbar .navbar-nav,
    .navbar .nav-item,
    .navbar .nav-link {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .navbar .navbar-collapse {
        display: flex !important;
    }

    .navbar .navbar-nav {
        display: flex !important;
    }
}

/* Force show specific navigation elements */
.navbar-nav:last-child,
.navbar-nav:last-child .nav-item,
.navbar-nav:last-child .nav-link {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.navbar-nav:last-child {
    display: flex !important;
}

/* Brand styling */
.navbar-brand {
    display: inline-block !important;
    padding-top: 0.3125rem !important;
    padding-bottom: 0.3125rem !important;
    margin-right: 1rem !important;
    font-size: 1.25rem !important;
    text-decoration: none !important;
    white-space: nowrap !important;
}

/* Toggler for mobile - Fixed to work properly */
@media (max-width: 991.98px) {
    .navbar-toggler {
        display: block !important;
        padding: 0.25rem 0.75rem !important;
        font-size: 1.25rem !important;
        line-height: 1 !important;
        background-color: transparent !important;
        border: 1px solid transparent !important;
        border-radius: 0.25rem !important;
    }

    .navbar-collapse {
        display: none !important;
    }

    .navbar-collapse.show {
        display: flex !important;
    }
}

@media (min-width: 992px) {
    .navbar-toggler {
        display: none !important;
    }
}

/* Fix for form elements in navbar */
.navbar form {
    display: inline-block !important;
}

.navbar form button {
    background: none !important;
    border: none !important;
    padding: 0.5rem 1rem !important;
    color: rgba(0,0,0,.55) !important;
    text-decoration: none !important;
    cursor: pointer !important;
}

.navbar form button:hover {
    color: rgba(0,0,0,.7) !important;
}

/* Ensure proper spacing */
.navbar-nav .nav-item + .nav-item {
    margin-left: 0 !important;
}

/* Override any conflicting styles */
.navbar .nav-link.btn {
    margin: 0 0.25rem !important;
}

/* Accessibility improvements */
.navbar-nav .nav-link:focus {
    outline: 2px solid #0d6efd !important;
    outline-offset: 2px !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .navbar-nav .nav-link {
        border: 1px solid currentColor !important;
    }
}
