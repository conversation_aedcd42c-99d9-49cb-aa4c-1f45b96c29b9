/* Navigation Override CSS - Responsive Mobile-First Design */

/* Base navbar always visible */
.navbar {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Desktop navigation - always show */
@media (min-width: 992px) {
    .navbar-collapse {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .navbar-nav {
        display: flex !important;
        flex-direction: row !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .nav-item,
    .nav-link {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
}

/* Mobile navigation - toggle behavior - FIXED */
@media (max-width: 991.98px) {
    .navbar-collapse {
        display: none !important;
    }

    .navbar-collapse.show {
        display: flex !important;
        flex-direction: column !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .navbar-collapse.show .navbar-nav {
        display: flex !important;
        flex-direction: column !important;
        visibility: visible !important;
        opacity: 1 !important;
        width: 100% !important;
    }

    .navbar-collapse.show .nav-item,
    .navbar-collapse.show .nav-link {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        width: 100% !important;
    }
}

/* Ensure login/register section is always visible */
.navbar-nav:last-child {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin-left: auto !important;
}

.navbar-nav:last-child .nav-item {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.navbar-nav:last-child .nav-link {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;    
    padding: 0.5rem 1rem !important;
}

/* Override any framework conflicts - but allow Bootstrap collapse to work */
.navbar .nav-link,
.navbar .nav-item {
    visibility: visible !important;
    opacity: 1 !important;
}


