/* Details Pages Styles */
/* Modern, responsive design for all details pages (Tasks, Operators, Projects, Parts, Machines) */

.details-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Hide any unwanted breadcrumb or numbered lists */
.details-container ol,
.details-container ul.breadcrumb,
.details-container .breadcrumb,
.details-container nav[aria-label="breadcrumb"],
.details-container .breadcrumb-list,
.details-container .breadcrumb-item,
body > ol,
body > ul.breadcrumb,
body > .breadcrumb,
body > nav[aria-label="breadcrumb"] {
    display: none !important;
}

/* Also hide any numbered lists that might appear before the details container */
body > div > ol,
body > div > ul.breadcrumb,
body > div > .breadcrumb,
body > div > nav[aria-label="breadcrumb"] {
    display: none !important;
}

/* Header Section */
.details-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 1.5rem 1rem;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
    border-radius: 1rem;
    margin: 0 1rem 2rem 1rem;
}

.details-title {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
}

.details-subtitle {
    font-size: 1.25rem;
    color: #6b7280;
    font-weight: 400;
    max-width: 600px;
    margin: 0 auto;
}

/* Main Content Layout */
.details-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.details-content.has-image {
    grid-template-columns: 1fr 400px;
}

/* Details Card */
.details-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(229, 231, 235, 0.8);
    transition: all 0.3s ease;
}

.details-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.details-card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.details-card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
}

.details-name {
    font-size: 2rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.details-id {
    font-size: 0.875rem;
    font-weight: 600;
    color: #8b5cf6;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.details-card-body {
    padding: 2rem;
}

/* Details Grid */
.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.details-section {
    background: #f8fafc;
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
}

.details-section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.details-section-title svg {
    width: 20px;
    height: 20px;
    color: #8b5cf6;
    flex-shrink: 0;
}

/* Detail Items */
.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e5e7eb;
    gap: 1rem;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 120px;
    flex-shrink: 0;
}

.detail-label svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

.detail-value {
    font-size: 0.875rem;
    color: #111827;
    font-weight: 600;
    text-align: right;
    word-break: break-word;
}

/* Status Indicators */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-active {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.status-inactive {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.status-pending {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
}

.status-completed {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.status-in-progress {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #bfdbfe;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
}

/* Priority Indicators */
.priority-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.priority-low {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;
}

.priority-medium {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
}

.priority-high {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.priority-critical {
    background: #7c2d12;
    color: white;
    border: 1px solid #7c2d12;
}

/* Image Section */
.details-image-container {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(229, 231, 235, 0.8);
    height: fit-content;
}

.details-image {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.details-image:hover {
    transform: scale(1.05);
}

.details-image-placeholder {
    width: 100%;
    height: 300px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    font-size: 1rem;
    font-weight: 500;
}

/* Action Buttons */
.details-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.details-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-width: 120px;
}

.details-btn svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

.details-btn-edit {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.details-btn-edit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(245, 158, 11, 0.4);
    color: white;
    text-decoration: none;
}

.details-btn-delete {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.details-btn-delete:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(239, 68, 68, 0.4);
    color: white;
    text-decoration: none;
}

.details-btn-back {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
}

.details-btn-back:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
    color: white;
    text-decoration: none;
}

/* Animations */
.fade-in-details {
    animation: fadeInDetails 0.6s ease-out;
}

@keyframes fadeInDetails {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}



/* Loading States */
.details-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.details-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #8b5cf6;
    border-radius: 50%;
    animation: detailsSpin 1s linear infinite;
}

@keyframes detailsSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Print Styles */
@media print {
    .details-actions {
        display: none !important;
    }

    .details-card {
        box-shadow: none !important;
        border: 1px solid #e5e7eb !important;
        break-inside: avoid;
    }

    .details-content.has-image {
        grid-template-columns: 1fr !important;
    }

    .details-image-container {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .details-container {
        padding: 0 0.75rem;
    }

    .details-content.has-image {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .details-image-container {
        order: -1;
        max-width: 500px;
        margin: 0 auto;
    }

    .details-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 1024px) {
    .details-container {
        padding: 0 0.5rem;
    }

    .details-header {
        margin: 0 0.5rem 1.5rem 0.5rem;
        padding: 1.25rem 1rem;
    }

    .details-title {
        font-size: 2.5rem;
    }

    .details-subtitle {
        font-size: 1.125rem;
    }

    .details-content.has-image {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .details-image-container {
        order: -1;
        max-width: 400px;
        margin: 0 auto;
    }

    .details-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .details-card-header,
    .details-card-body {
        padding: 1.5rem;
    }

    .details-section {
        padding: 1.25rem;
    }
}

/* iPad Mini and Tablet Portrait */
@media (max-width: 768px) {
    .details-container {
        padding: 0 0.25rem;
    }

    .details-header {
        padding: 1rem 0.75rem;
        margin: 0 0.25rem 1.5rem 0.25rem;
        border-radius: 0.75rem;
    }

    .details-title {
        font-size: 2rem;
        line-height: 1.2;
        margin-bottom: 0.75rem;
    }

    .details-subtitle {
        font-size: 1rem;
        padding: 0 0.5rem;
        line-height: 1.4;
    }

    .details-name {
        font-size: 1.5rem;
        line-height: 1.3;
    }

    .details-card-header,
    .details-card-body {
        padding: 1.25rem;
    }

    .details-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .details-grid {
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .details-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .details-btn {
        width: 100%;
        justify-content: center;
        padding: 0.875rem 1rem;
        font-size: 0.9rem;
    }

    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        padding: 0.5rem 0;
    }

    .detail-label {
        min-width: auto;
        font-size: 0.8rem;
        font-weight: 600;
        color: #4b5563;
    }

    .detail-value {
        text-align: left;
        font-size: 0.9rem;
        font-weight: 600;
        color: #111827;
    }

    .details-image {
        height: 250px;
    }

    .details-image-placeholder {
        height: 250px;
        font-size: 0.875rem;
    }

    .status-badge,
    .priority-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.6rem;
    }
}

/* Mobile Devices */
@media (max-width: 480px) {
    .details-container {
        padding: 0 0.125rem;
    }

    .details-header {
        padding: 0.75rem 0.5rem;
        margin: 0 0.125rem 1rem 0.125rem;
        border-radius: 0.5rem;
    }

    .details-title {
        font-size: 1.75rem;
        line-height: 1.1;
        margin-bottom: 0.5rem;
    }

    .details-subtitle {
        font-size: 0.9rem;
        padding: 0 0.25rem;
        line-height: 1.4;
    }

    .details-name {
        font-size: 1.25rem;
        line-height: 1.2;
        margin-bottom: 0.75rem;
    }

    .details-id {
        font-size: 0.75rem;
        margin-bottom: 0.25rem;
    }

    .details-card-header,
    .details-card-body {
        padding: 1rem;
    }

    .details-section {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
        border-radius: 0.5rem;
    }

    .details-grid {
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .details-section-title {
        font-size: 1rem;
        margin-bottom: 0.75rem;
    }

    .details-section-title svg {
        width: 16px;
        height: 16px;
    }

    .detail-item {
        padding: 0.375rem 0;
        gap: 0.25rem;
    }

    .detail-label {
        font-size: 0.75rem;
        font-weight: 600;
        color: #6b7280;
    }

    .detail-value {
        font-size: 0.85rem;
        font-weight: 600;
        color: #111827;
    }

    .detail-label svg {
        width: 12px;
        height: 12px;
    }

    .details-actions {
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .details-btn {
        padding: 0.75rem 0.875rem;
        font-size: 0.85rem;
        border-radius: 0.375rem;
    }

    .details-btn svg {
        width: 14px;
        height: 14px;
    }

    .details-image {
        height: 200px;
        border-radius: 0.5rem;
    }

    .details-image-placeholder {
        height: 200px;
        font-size: 0.8rem;
        border-radius: 0.5rem;
    }

    .status-badge,
    .priority-badge {
        font-size: 0.65rem;
        padding: 0.15rem 0.5rem;
        border-radius: 9999px;
    }

    .status-indicator {
        width: 6px;
        height: 6px;
    }

    /* Optimize content spacing for small screens */
    .details-content {
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .details-content.has-image {
        gap: 1rem;
    }

    .details-image-container {
        max-width: 100%;
        margin: 0;
    }
}

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
    .details-card {
        background: #1f2937;
        border-color: #374151;
    }

    .details-card-header {
        background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
    }

    .details-name {
        color: #f9fafb;
    }

    .details-card-body {
        background: #1f2937;
    }

    .details-section {
        background: #374151;
        border-color: #4b5563;
    }

    .details-section-title {
        color: #f9fafb;
    }

    .detail-item {
        border-color: #4b5563;
    }

    .detail-label {
        color: #d1d5db;
    }

    .detail-value {
        color: #f9fafb;
    }
}

/* iPhone 14 Pro and similar */
@media (max-width: 430px) {
    .details-container {
        padding: 0 0.125rem;
    }

    .details-header {
        padding: 0.625rem 0.375rem;
        margin: 0 0.125rem 0.875rem 0.125rem;
    }

    .details-title {
        font-size: 1.625rem;
        margin-bottom: 0.5rem;
    }

    .details-subtitle {
        font-size: 0.85rem;
        padding: 0 0.25rem;
    }

    .details-name {
        font-size: 1.25rem;
    }

    .details-card-header,
    .details-card-body {
        padding: 0.875rem;
    }

    .details-section {
        padding: 0.625rem;
    }

    .details-btn {
        padding: 0.625rem 0.875rem;
        font-size: 0.8rem;
    }
}

/* Extra Small Screens */
@media (max-width: 360px) {
    .details-container {
        padding: 0 0.0625rem;
    }

    .details-header {
        padding: 0.5rem 0.25rem;
        margin: 0 0.0625rem 0.75rem 0.0625rem;
    }

    .details-title {
        font-size: 1.5rem;
    }

    .details-subtitle {
        font-size: 0.8rem;
        padding: 0 0.125rem;
    }

    .details-name {
        font-size: 1.125rem;
    }

    .details-card-header,
    .details-card-body {
        padding: 0.75rem;
    }

    .details-section {
        padding: 0.5rem;
    }

    .details-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }
}

/* Landscape Orientation for Mobile */
@media (max-height: 500px) and (orientation: landscape) {
    .details-header {
        padding: 0.5rem 0;
        margin-bottom: 0.75rem;
    }

    .details-title {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .details-subtitle {
        font-size: 0.85rem;
    }

    .details-card-header {
        padding: 1rem;
    }

    .details-section {
        padding: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .details-grid {
        gap: 0.5rem;
        margin-bottom: 0.75rem;
    }
}

/* High DPI Screens */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .details-image {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Hover Support Detection */
@media (hover: hover) {
    .details-card:hover {
        transform: translateY(-2px);
    }

    .details-btn:hover {
        transform: translateY(-1px) scale(1.02);
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .details-card,
    .details-btn,
    .details-image,
    .fade-in-details {
        transition: none !important;
        animation: none !important;
        transform: none !important;
    }
}
