/* CMMs (Machines) Page Styles */
/* Modern, responsive design for the CMMs listing page */

.machines-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header Section */
.machines-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem 0;
}

.machines-title {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
}

.machines-subtitle {
    font-size: 1.25rem;
    color: #6b7280;
    font-weight: 400;
    max-width: 600px;
    margin: 0 auto;
}

/* Add New Machine Button */
.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    color: white;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(16, 185, 129, 0.4);
    color: white;
    text-decoration: none;
}

/* Search and Filter Section */
.search-section {
    background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 3rem;
    backdrop-filter: blur(10px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.search-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    align-items: end;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    color: white;
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.form-input,
.form-select {
    padding: 0.75rem 1rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.form-select option {
    background: #374151;
    color: white;
}

.search-buttons {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(59, 130, 246, 0.4);
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    text-decoration: none;
}

/* Machines Grid */
.machines-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* Machine Card */
.machine-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid rgba(229, 231, 235, 0.8);
}

.machine-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.machine-card-image-container {
    overflow: hidden;
    position: relative;
    height: 220px;
}

.machine-image-placeholder {
    width: 100%;
    height: 100%;
    position: relative;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.machine-card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    position: absolute;
    top: 0;
    left: 0;
}

.machine-image-fallback {
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 2rem;
    color: #64748b;
}

.machine-image-placeholder.image-error .machine-image-fallback {
    display: flex;
}

.machine-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 1rem;
    color: #8b5cf6;
}

.machine-icon svg {
    width: 100%;
    height: 100%;
}

.machine-brand {
    font-size: 1.25rem;
    font-weight: 700;
    color: #374151;
    margin-bottom: 0.25rem;
}

.machine-type {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Brand-specific styling */
.machine-image-placeholder[data-brand="zeiss"] {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
}

.machine-image-placeholder[data-brand="zeiss"] .machine-brand {
    color: white;
}

.machine-image-placeholder[data-brand="zeiss"] .machine-type {
    color: rgba(255, 255, 255, 0.8);
}

.machine-image-placeholder[data-brand="zeiss"] .machine-icon {
    color: white;
}

.machine-image-placeholder[data-brand="mitutoyo"] {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
}

.machine-image-placeholder[data-brand="mitutoyo"] .machine-brand {
    color: white;
}

.machine-image-placeholder[data-brand="mitutoyo"] .machine-type {
    color: rgba(255, 255, 255, 0.8);
}

.machine-image-placeholder[data-brand="mitutoyo"] .machine-icon {
    color: white;
}

.machine-image-placeholder[data-brand="hexagon"] {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
}

.machine-image-placeholder[data-brand="hexagon"] .machine-brand {
    color: white;
}

.machine-image-placeholder[data-brand="hexagon"] .machine-type {
    color: rgba(255, 255, 255, 0.8);
}

.machine-image-placeholder[data-brand="hexagon"] .machine-icon {
    color: white;
}

.machine-image-placeholder[data-brand="brown"] {
    background: linear-gradient(135deg, #92400e 0%, #d97706 100%);
}

.machine-image-placeholder[data-brand="brown"] .machine-brand {
    color: white;
}

.machine-image-placeholder[data-brand="brown"] .machine-type {
    color: rgba(255, 255, 255, 0.8);
}

.machine-image-placeholder[data-brand="brown"] .machine-icon {
    color: white;
}

.machine-image-placeholder[data-brand="coord3"] {
    background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%);
}

.machine-image-placeholder[data-brand="coord3"] .machine-brand {
    color: white;
}

.machine-image-placeholder[data-brand="coord3"] .machine-type {
    color: rgba(255, 255, 255, 0.8);
}

.machine-image-placeholder[data-brand="coord3"] .machine-icon {
    color: white;
}

.machine-card:hover .machine-card-image {
    transform: scale(1.05);
}

.machine-card-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.machine-card:hover .machine-card-image-overlay {
    opacity: 1;
}

.machine-card-content {
    padding: 1.5rem;
}

.machine-card-header {
    margin-bottom: 1rem;
}

.machine-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.machine-status {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: inline-block;
}

.machine-status-calibrated {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.machine-status-not-calibrated {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.machine-details {
    margin-bottom: 1.5rem;
}

.machine-detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.machine-detail-item:last-child {
    border-bottom: none;
}

.machine-detail-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.machine-detail-value {
    font-size: 0.875rem;
    color: #111827;
    font-weight: 600;
}

.capacity-indicator {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    color: #374151;
    padding: 0.25rem 0.75rem;
    border-radius: 0.375rem;
    font-weight: 600;
    border: 1px solid #e5e7eb;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.capacity-indicator::before {
    content: '⚙️';
    font-size: 0.75rem;
}

/* Machine type indicators */
.machine-type-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.9);
    color: #374151;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Calibration status indicators */
.calibration-warning {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
    padding: 0.5rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.calibration-warning::before {
    content: '⚠️';
}

.calibration-ok {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
    padding: 0.5rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.calibration-ok::before {
    content: '✅';
}

.machine-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.action-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
}

.action-btn svg {
    width: 14px;
    height: 14px;
    margin-right: 0.25rem;
    flex-shrink: 0;
}

.action-btn-details {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.action-btn-details:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px -2px rgba(16, 185, 129, 0.4);
    color: white;
    text-decoration: none;
}

.action-btn-edit {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.action-btn-edit:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px -2px rgba(245, 158, 11, 0.4);
    color: white;
    text-decoration: none;
}

.action-btn-delete {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.action-btn-delete:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px -2px rgba(239, 68, 68, 0.4);
    color: white;
    text-decoration: none;
}

.w-full {
    width: 100%;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6b7280;
}

.empty-state h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.empty-state p {
    font-size: 1rem;
    max-width: 400px;
    margin: 0 auto;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 3rem;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    padding: 1rem;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.page-item {
    list-style: none;
}

.page-link {
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    color: #6b7280;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.page-link:hover {
    background: #f3f4f6;
    color: #374151;
    text-decoration: none;
}

.page-item.active .page-link {
    background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
    color: white;
}

.page-item.disabled .page-link {
    color: #d1d5db;
    cursor: not-allowed;
}

.page-item.disabled .page-link:hover {
    background: transparent;
    color: #d1d5db;
}

/* Animations */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #8b5cf6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Focus States */
.form-input:focus,
.form-select:focus,
.action-btn:focus {
    outline: 2px solid #8b5cf6;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .search-section,
    .pagination-container,
    .machine-actions {
        display: none !important;
    }

    .machines-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .machine-card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #e5e7eb !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .machines-title {
        font-size: 2rem;
    }

    .machines-subtitle {
        font-size: 1rem;
    }

    .search-form {
        grid-template-columns: 1fr;
    }

    .machines-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .machine-actions {
        flex-direction: column;
    }

    .action-btn {
        width: 100%;
    }

    .search-buttons {
        flex-direction: column;
        width: 100%;
    }

    .btn-primary,
    .btn-secondary {
        width: 100%;
    }

    .pagination {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .page-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 480px) {
    .machines-container {
        padding: 0 0.5rem;
    }

    .search-section {
        padding: 1rem;
        margin-bottom: 2rem;
    }

    .machine-card-content {
        padding: 1rem;
    }

    .machine-title {
        font-size: 1.25rem;
    }

    .action-btn svg {
        width: 12px;
        height: 12px;
    }
}

/* Machine Assignment Styles */
.machine-assignment-info {
    margin: 1rem 0;
    padding: 1rem;
    border-radius: 0.75rem;
    border: 2px solid;
    transition: all 0.3s ease;
}

.machine-assignment-info.occupied {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border-color: #f59e0b;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
}

.machine-assignment-info.available {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    border-color: #10b981;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.assignment-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.assignment-icon {
    width: 1.5rem;
    height: 1.5rem;
    flex-shrink: 0;
}

.assignment-icon.occupied {
    color: #d97706;
}

.assignment-icon.available {
    color: #059669;
}

.assignment-status {
    font-weight: 600;
    font-size: 1rem;
}

.machine-assignment-info.occupied .assignment-status {
    color: #92400e;
}

.machine-assignment-info.available .assignment-status {
    color: #047857;
}

.assignment-details {
    margin-left: 2.25rem;
    font-size: 0.875rem;
}

.assignment-task,
.assignment-project,
.assignment-operators,
.assignment-task-status {
    margin-bottom: 0.5rem;
    color: #374151;
}

.assignment-task strong,
.assignment-project strong,
.assignment-operators strong,
.assignment-task-status strong {
    color: #1f2937;
}

.task-status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.task-status-badge.status-open {
    background: #dbeafe;
    color: #1e40af;
}

.task-status-badge.status-in-progress {
    background: #fef3c7;
    color: #d97706;
}

.task-status-badge.status-review {
    background: #e0e7ff;
    color: #6366f1;
}

.availability-message {
    color: #374151;
    font-size: 0.875rem;
    margin: 0;
}

.availability-message.warning {
    color: #d97706;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.availability-message.warning svg {
    width: 1rem;
    height: 1rem;
    flex-shrink: 0;
}

/* Details Page Assignment Styles */
.assignment-section {
    grid-column: 1 / -1;
    margin-top: 2rem;
}

.assignment-status {
    padding: 1.5rem;
    border-radius: 1rem;
    border: 2px solid;
    margin-top: 1rem;
}

.assignment-status.occupied {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border-color: #f59e0b;
}

.assignment-status.available {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    border-color: #10b981;
}

.assignment-status .assignment-header {
    margin-bottom: 1.5rem;
}

.assignment-status .assignment-icon {
    width: 2rem;
    height: 2rem;
}

.assignment-status.occupied .assignment-icon,
.assignment-status.occupied .status-text {
    color: #d97706;
}

.assignment-status.available .assignment-icon,
.assignment-status.available .status-text {
    color: #059669;
}

.status-text {
    font-size: 1.125rem;
    font-weight: 600;
}

.assignment-details .detail-item {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.assignment-details .detail-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.task-link {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.2s ease;
}

.task-link:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

.priority-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.priority-badge.priority-low {
    background: #d1fae5;
    color: #047857;
}

.priority-badge.priority-medium {
    background: #fef3c7;
    color: #d97706;
}

.priority-badge.priority-high {
    background: #fee2e2;
    color: #dc2626;
}

.priority-badge.priority-urgent {
    background: #fde2e7;
    color: #be185d;
}

.operators-section .detail-label {
    align-self: flex-start;
    margin-top: 0.25rem;
}

.operators-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.operator-card {
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    padding: 0.75rem;
    transition: all 0.2s ease;
}

.operator-card:hover {
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.operator-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.operator-name {
    font-weight: 600;
    color: #1f2937;
}

.operator-email {
    font-size: 0.875rem;
    color: #6b7280;
}

.availability-details {
    margin-top: 1rem;
}

.availability-message.success {
    color: #047857;
    font-weight: 500;
}

/* Responsive Design for Assignment Info */
@media (max-width: 768px) {
    .assignment-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .assignment-details {
        margin-left: 0;
    }

    .operators-list {
        gap: 0.5rem;
    }

    .operator-card {
        padding: 0.5rem;
    }
}
