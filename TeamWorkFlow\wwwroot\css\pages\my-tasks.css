/* My Tasks Page Styling */
/* Modern admin-style table design for personal task collection */

/* Page Animations */
.admin-container {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading States */
.my-tasks-table.loading {
    opacity: 0.7;
    pointer-events: none;
    position: relative;
}

.my-tasks-table.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    animation: tableLoading 1.5s infinite;
    z-index: 10;
}

@keyframes tableLoading {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Interactive Features */
.task-row {
    cursor: pointer;
    position: relative;
}

.task-row::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: transparent;
    transition: all 0.3s ease;
}

.task-row:hover::before {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.task-row.priority-critical::before {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.task-row.priority-high:hover::before {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

/* Table Container */
.admin-table-container {
    background: white;
    border-radius: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    margin-bottom: 2rem;
    border: 1px solid rgba(229, 231, 235, 0.6);
}

/* Mobile Cards Container - Hidden by default */
.mobile-cards-container {
    display: none;
}

.admin-table-header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 2rem;
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
}

.admin-table-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #0f172a;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.admin-table-title svg {
    width: 24px;
    height: 24px;
    color: #3b82f6;
}

.admin-table-description {
    color: #64748b;
    font-size: 1rem;
    margin: 0;
}

/* Enhanced Table Styling */
.my-tasks-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: white;
}

.my-tasks-table thead {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

.my-tasks-table thead th {
    padding: 1.25rem 1rem;
    text-align: left;
    font-weight: 700;
    font-size: 0.875rem;
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: none;
    position: relative;
    white-space: nowrap;
}

.my-tasks-table thead th:first-child {
    border-top-left-radius: 0;
}

.my-tasks-table thead th:last-child {
    border-top-right-radius: 0;
}

.my-tasks-table thead th svg {
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
}

.my-tasks-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
}

.my-tasks-table tbody tr:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.my-tasks-table tbody tr:last-child {
    border-bottom: none;
}

.my-tasks-table tbody td {
    padding: 1.25rem 1rem;
    vertical-align: middle;
    border: none;
    font-size: 0.875rem;
}

/* Cell Content Styling */
.project-number {
    font-weight: 700;
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.8rem;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.task-name {
    font-weight: 600;
    color: #0f172a;
    font-size: 0.95rem;
    line-height: 1.4;
}

.task-description {
    color: #64748b;
    font-size: 0.875rem;
    line-height: 1.5;
    max-width: 200px;
    display: block;
}

.date-value {
    color: #475569;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.5rem 0.875rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: 1px solid;
    position: relative;
    transition: all 0.3s ease;
    cursor: default;
}

.status-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.status-badge::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    display: inline-block;
}

.status-pending {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
    border-color: #f59e0b;
}

.status-pending::before {
    background: #f59e0b;
}

.status-in-progress {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1e40af;
    border-color: #3b82f6;
}

.status-in-progress::before {
    background: #3b82f6;
    animation: progressPulse 2s infinite;
}

@keyframes progressPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

.status-completed {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    color: #166534;
    border-color: #10b981;
}

.status-completed::before {
    background: #10b981;
}

.status-cancelled {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #991b1b;
    border-color: #ef4444;
}

.status-cancelled::before {
    background: #ef4444;
}

/* Priority Badges */
.priority-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.5rem 0.875rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: 1px solid;
    transition: all 0.3s ease;
    cursor: default;
}

.priority-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.priority-badge::before {
    content: '';
    width: 0;
    height: 0;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    display: inline-block;
}

.priority-low {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    color: #374151;
    border-color: #9ca3af;
}

.priority-low::before {
    border-bottom: 5px solid #9ca3af;
}

.priority-medium {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
    border-color: #f59e0b;
}

.priority-medium::before {
    border-bottom: 5px solid #f59e0b;
}

.priority-high {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #991b1b;
    border-color: #ef4444;
}

.priority-high::before {
    border-bottom: 5px solid #ef4444;
}

.priority-critical {
    background: linear-gradient(135deg, #7c2d12 0%, #991b1b 100%);
    color: white;
    border-color: #dc2626;
    animation: criticalPulse 2s infinite;
}

.priority-critical::before {
    border-bottom: 5px solid #fca5a5;
}

@keyframes criticalPulse {
    0%, 100% { box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.4); }
    50% { box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.1); }
}

/* Action Button */
.remove-form {
    margin: 0;
}

.admin-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.625rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.admin-btn svg {
    width: 16px;
    height: 16px;
}

.admin-btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border-color: rgba(239, 68, 68, 0.3);
}

.admin-btn-danger:hover {
    background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(239, 68, 68, 0.4);
    text-decoration: none;
}

.admin-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
    z-index: 0;
}

.admin-btn:hover::before {
    left: 100%;
}

.admin-btn svg,
.admin-btn span {
    position: relative;
    z-index: 1;
}

/* Confirmation Animation */
.remove-form.confirming .admin-btn {
    animation: confirmShake 0.5s ease-in-out;
}

@keyframes confirmShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

/* Empty State */
.admin-empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.admin-empty-state svg {
    width: 64px;
    height: 64px;
    color: #9ca3af;
    margin-bottom: 1rem;
}

.admin-empty-state h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #374151;
    margin-bottom: 0.5rem;
}

.admin-empty-state p {
    color: #6b7280;
    font-size: 1rem;
    margin-bottom: 1.5rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .admin-table-header {
        padding: 1.5rem;
    }
    
    .my-tasks-table thead th,
    .my-tasks-table tbody td {
        padding: 1rem 0.75rem;
    }
    
    .task-description {
        max-width: 150px;
    }
}

@media (max-width: 768px) {
    .admin-table-header {
        padding: 1rem;
    }
    
    .admin-table-title {
        font-size: 1.25rem;
    }
    
    .my-tasks-table {
        font-size: 0.8rem;
    }
    
    .my-tasks-table thead th,
    .my-tasks-table tbody td {
        padding: 0.75rem 0.5rem;
    }
    
    .task-description {
        max-width: 120px;
    }
    
    .status-badge,
    .priority-badge {
        padding: 0.375rem 0.625rem;
        font-size: 0.7rem;
    }
    
    .admin-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 640px) {
    .admin-container {
        padding: 0 0.75rem;
    }

    .admin-header {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .admin-title {
        font-size: 2rem;
    }

    .admin-subtitle {
        font-size: 0.95rem;
    }

    /* Hide less important columns on mobile */
    .my-tasks-table thead th:nth-child(3),
    .my-tasks-table tbody td:nth-child(3) {
        display: none;
    }

    .my-tasks-table thead th:nth-child(7),
    .my-tasks-table tbody td:nth-child(7),
    .my-tasks-table thead th:nth-child(8),
    .my-tasks-table tbody td:nth-child(8) {
        display: none;
    }

    /* Make remaining columns more compact */
    .my-tasks-table thead th,
    .my-tasks-table tbody td {
        padding: 0.5rem 0.25rem;
        font-size: 0.75rem;
    }

    .project-number {
        font-size: 0.7rem;
        padding: 0.2rem 0.5rem;
    }

    .task-name {
        font-size: 0.8rem;
        line-height: 1.3;
    }

    .status-badge,
    .priority-badge {
        padding: 0.25rem 0.5rem;
        font-size: 0.65rem;
        gap: 0.25rem;
    }

    .status-badge::before,
    .priority-badge::before {
        width: 4px;
        height: 4px;
    }

    .admin-btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.7rem;
        min-width: auto;
    }

    .admin-btn svg {
        width: 12px;
        height: 12px;
    }

    .admin-empty-state {
        padding: 2rem 1rem;
    }

    .admin-empty-state svg {
        width: 48px;
        height: 48px;
    }

    .admin-empty-state h2 {
        font-size: 1.25rem;
    }

    .admin-empty-state p {
        font-size: 0.875rem;
    }
}

@media (max-width: 480px) {
    .admin-container {
        padding: 0 0.5rem;
    }

    .admin-header {
        padding: 1.5rem 1rem;
        margin-bottom: 1.5rem;
        border-radius: 1rem;
    }

    .admin-title {
        font-size: 1.75rem;
        margin-bottom: 0.5rem;
    }

    .admin-subtitle {
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    .admin-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.75rem;
    }

    /* Mobile Card Layout */
    .admin-table-container {
        background: transparent;
        box-shadow: none;
        border: none;
        border-radius: 0;
        padding: 0;
    }

    .admin-table-header {
        background: white;
        border-radius: 1rem;
        padding: 1.25rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .admin-table-title {
        font-size: 1.125rem;
        margin-bottom: 0.5rem;
    }

    .admin-table-title svg {
        width: 20px;
        height: 20px;
    }

    .admin-table-description {
        font-size: 0.875rem;
        color: #6b7280;
    }

    /* Hide table, show cards */
    .table-responsive {
        display: none;
    }

    /* Mobile Cards Container */
    .mobile-cards-container {
        display: block;
    }

    .task-mobile-card {
        background: white;
        border-radius: 1rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        border: 1px solid rgba(229, 231, 235, 0.6);
        transition: all 0.3s ease;
        position: relative;
    }

    .task-mobile-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .task-mobile-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    }

    .task-mobile-card.priority-critical::before {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        animation: criticalGlow 2s infinite;
    }

    .task-mobile-card.priority-high::before {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }

    .task-mobile-card-header {
        padding: 1rem;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-bottom: 1px solid rgba(229, 231, 235, 0.5);
    }

    .task-mobile-project {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
    }

    .task-mobile-project-number {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 700;
    }

    .task-mobile-name {
        font-size: 1.125rem;
        font-weight: 700;
        color: #0f172a;
        line-height: 1.4;
        margin-bottom: 0.5rem;
    }

    .task-mobile-status-priority {
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
    }

    .task-mobile-card-body {
        padding: 1rem;
    }

    .task-mobile-description {
        color: #6b7280;
        font-size: 0.875rem;
        line-height: 1.5;
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: rgba(249, 250, 251, 0.8);
        border-radius: 0.5rem;
        border-left: 3px solid #e5e7eb;
    }

    .task-mobile-dates {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .task-mobile-date-item {
        background: rgba(249, 250, 251, 0.8);
        padding: 0.75rem;
        border-radius: 0.5rem;
        border: 1px solid rgba(229, 231, 235, 0.5);
    }

    .task-mobile-date-label {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        font-size: 0.75rem;
        font-weight: 600;
        color: #6b7280;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 0.25rem;
    }

    .task-mobile-date-label svg {
        width: 12px;
        height: 12px;
    }

    .task-mobile-date-value {
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
    }

    .task-mobile-actions {
        padding: 1rem;
        background: rgba(249, 250, 251, 0.5);
        border-top: 1px solid rgba(229, 231, 235, 0.5);
    }

    .task-mobile-remove-btn {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.875rem 1rem;
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
        border: none;
        border-radius: 0.75rem;
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
    }

    .task-mobile-remove-btn:hover {
        background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
    }

    .task-mobile-remove-btn svg {
        width: 16px;
        height: 16px;
    }

    /* Enhanced status and priority badges for mobile */
    .task-mobile-card .status-badge,
    .task-mobile-card .priority-badge {
        font-size: 0.7rem;
        padding: 0.375rem 0.75rem;
        gap: 0.25rem;
    }

    .task-mobile-card .status-badge::before,
    .task-mobile-card .priority-badge::before {
        width: 5px;
        height: 5px;
    }

    /* Empty state for mobile */
    .admin-empty-state {
        padding: 2rem 1rem;
        margin: 0 0.5rem;
        border-radius: 1rem;
    }

    .admin-empty-state svg {
        width: 48px;
        height: 48px;
        margin-bottom: 1rem;
    }

    .admin-empty-state h2 {
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }

    .admin-empty-state p {
        font-size: 0.875rem;
        margin-bottom: 1.5rem;
    }

    .dashboard-card-action {
        padding: 0.75rem 1.5rem;
        font-size: 0.875rem;
    }
}
