/* Parts Page Styles */
/* Modern, responsive design for the Parts listing page */

.parts-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header Section */
.parts-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem 0;
}

.parts-title {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
}

.parts-subtitle {
    font-size: 1.25rem;
    color: #6b7280;
    font-weight: 400;
    max-width: 600px;
    margin: 0 auto;
}

/* Add New Part Button */
.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    color: white;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(16, 185, 129, 0.4);
    color: white;
    text-decoration: none;
}

/* Search and Filter Section */
.search-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 3rem;
    backdrop-filter: blur(10px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.search-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    align-items: end;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    color: white;
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.form-input,
.form-select {
    padding: 0.75rem 1rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.form-select option {
    background: #374151;
    color: white;
}

.search-buttons {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(59, 130, 246, 0.4);
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    text-decoration: none;
}

/* Parts Grid */
.parts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* Part Card */
.part-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid rgba(229, 231, 235, 0.8);
}

.part-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.part-card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    transition: transform 0.3s ease;
}

.part-card:hover .part-card-image {
    transform: scale(1.05);
}

.part-card-image-container {
    overflow: hidden;
    position: relative;
}

.part-card-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.part-card:hover .part-card-image-overlay {
    opacity: 1;
}

.part-card-content {
    padding: 1.5rem;
}

.part-card-header {
    margin-bottom: 1rem;
}

.part-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.part-project {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    display: inline-block;
    border: 1px solid #e5e7eb;
}

.part-status {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: inline-block;
    margin-top: 0.5rem;
}

.part-status-approved {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.part-status-pending {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
}

.part-status-rejected {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.part-status-in-progress {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #bfdbfe;
}

.part-details {
    margin-bottom: 1.5rem;
}

.part-detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.part-detail-item:last-child {
    border-bottom: none;
}

.part-detail-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.part-detail-value {
    font-size: 0.875rem;
    color: #111827;
    font-weight: 600;
}

.part-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.action-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
}

.action-btn svg {
    width: 14px;
    height: 14px;
    margin-right: 0.25rem;
    flex-shrink: 0;
}

.action-btn-details {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.action-btn-details:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px -2px rgba(16, 185, 129, 0.4);
    color: white;
    text-decoration: none;
}

.action-btn-edit {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.action-btn-edit:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px -2px rgba(245, 158, 11, 0.4);
    color: white;
    text-decoration: none;
}

.action-btn-delete {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.action-btn-delete:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px -2px rgba(239, 68, 68, 0.4);
    color: white;
    text-decoration: none;
}

.w-full {
    width: 100%;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6b7280;
}

.empty-state h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.empty-state p {
    font-size: 1rem;
    max-width: 400px;
    margin: 0 auto;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 3rem;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    padding: 1rem;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.page-item {
    list-style: none;
}

.page-link {
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    color: #6b7280;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.page-link:hover {
    background: #f3f4f6;
    color: #374151;
    text-decoration: none;
}

.page-item.active .page-link {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.page-item.disabled .page-link {
    color: #d1d5db;
    cursor: not-allowed;
}

.page-item.disabled .page-link:hover {
    background: transparent;
    color: #d1d5db;
}

/* Animations */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Focus States */
.form-input:focus,
.form-select:focus,
.action-btn:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .search-section,
    .pagination-container,
    .part-actions {
        display: none !important;
    }

    .parts-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .part-card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #e5e7eb !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .parts-title {
        font-size: 2rem;
    }

    .parts-subtitle {
        font-size: 1rem;
    }

    .search-form {
        grid-template-columns: 1fr;
    }

    .parts-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .part-actions {
        flex-direction: column;
    }

    .action-btn {
        width: 100%;
    }

    .search-buttons {
        flex-direction: column;
        width: 100%;
    }

    .btn-primary,
    .btn-secondary {
        width: 100%;
    }

    .pagination {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .page-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 480px) {
    .parts-container {
        padding: 0 0.5rem;
    }

    .search-section {
        padding: 1rem;
        margin-bottom: 2rem;
    }

    .part-card-content {
        padding: 1rem;
    }

    .action-btn svg {
        width: 12px;
        height: 12px;
    }
}
