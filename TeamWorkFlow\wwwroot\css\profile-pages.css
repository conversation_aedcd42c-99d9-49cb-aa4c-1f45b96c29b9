/* Profile Pages CSS - Modern & Mobile-Friendly Design */
/* Enhanced styling for Profile/Account Management pages */

/* Profile Page Container */
.profile-page {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.profile-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Profile Header */
.profile-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.profile-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.profile-header-content {
    position: relative;
    z-index: 2;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 1rem;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.profile-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.profile-email {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/* Profile Navigation */
.profile-nav {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.profile-nav .nav-pills {
    gap: 0.5rem;
}

.profile-nav .nav-link {
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    color: #64748b;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.profile-nav .nav-link:hover {
    background: #f1f5f9;
    color: #475569;
    transform: translateY(-2px);
}

.profile-nav .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Profile Content Card */
.profile-content {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.profile-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.profile-content-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f5f9;
}

.profile-content-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.profile-content-subtitle {
    color: #64748b;
    font-size: 1rem;
    margin-bottom: 0;
}

/* Enhanced Form Styling */
.profile-form {
    margin-top: 1.5rem;
}

.profile-form .form-floating {
    margin-bottom: 1.5rem;
    position: relative;
}

.profile-form .form-control {
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8fafc;
}

.profile-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
    transform: translateY(-2px);
}

.profile-form .form-control:disabled {
    background: #f1f5f9;
    color: #64748b;
    border-color: #e2e8f0;
}

.profile-form .form-floating > label {
    color: #64748b;
    font-weight: 500;
    padding: 1rem;
}

.profile-form .form-control:focus ~ label,
.profile-form .form-control:not(:placeholder-shown) ~ label {
    color: #667eea;
    font-weight: 600;
}

/* Status Messages */
.status-message {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-weight: 500;
    border: 2px solid transparent;
}

.status-message.success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.status-message.error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.status-message.info {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

/* Enhanced Buttons */
.profile-btn {
    padding: 0.875rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.profile-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.profile-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    color: white;
}

.profile-btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.profile-btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
    color: white;
}

.profile-btn-secondary {
    background: #f8fafc;
    color: #475569;
    border-color: #e2e8f0;
}

.profile-btn-secondary:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    transform: translateY(-2px);
    color: #334155;
}

/* Email Verification Badge */
.email-verified {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-left: 1rem;
}

.email-unverified {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-left: 1rem;
}

/* Security Indicators */
.security-indicator {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 12px;
    margin-bottom: 1rem;
    border-left: 4px solid #667eea;
}

.security-indicator-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: bold;
}

.security-indicator.strong {
    border-left-color: #10b981;
}

.security-indicator.strong .security-indicator-icon {
    background: #10b981;
    color: white;
}

.security-indicator.weak {
    border-left-color: #ef4444;
}

.security-indicator.weak .security-indicator-icon {
    background: #ef4444;
    color: white;
}

/* Personal Data Actions */
.data-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.data-warning {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border: 2px solid #f59e0b;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    color: #92400e;
}

.data-warning-title {
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #78350f;
}

/* Mobile Navigation Toggle */
.profile-nav-toggle {
    display: none;
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    font-weight: 600;
    color: #475569;
    width: 100%;
    text-align: left;
}

.profile-nav-toggle:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
}



/* Responsive Design */
@media (max-width: 768px) {
    .profile-page {
        padding: 1rem 0;
    }

    .profile-container {
        padding: 0 0.5rem;
    }

    .profile-header {
        padding: 1.5rem;
        border-radius: 15px;
        text-align: center;
    }

    .profile-avatar {
        margin: 0 auto 1rem;
    }



    .profile-nav {
        padding: 1rem;
    }

    .profile-nav-toggle {
        display: block;
    }

    .profile-nav .nav-pills {
        display: none;
        flex-direction: column;
        margin-top: 1rem;
    }

    .profile-nav.open .nav-pills {
        display: flex;
    }

    .profile-nav .nav-link {
        padding: 1rem;
        text-align: center;
    }

    .profile-content {
        padding: 1.5rem;
        border-radius: 15px;
    }

    .profile-content-title {
        font-size: 1.5rem;
    }

    .data-actions {
        flex-direction: column;
    }

    .profile-btn {
        width: 100%;
        justify-content: center;
        padding: 1rem;
    }

    .profile-stats {
        margin-top: 1rem;
    }

    .profile-stats .stat-number {
        font-size: 1.25rem;
    }

    .profile-breadcrumb {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .profile-header {
        padding: 1rem;
    }
    
    .profile-name {
        font-size: 1.25rem;
    }
    
    .profile-content {
        padding: 1rem;
    }
    
    .profile-content-title {
        font-size: 1.25rem;
    }
    
    .profile-form .form-control {
        padding: 0.875rem;
    }
    
    .profile-form .form-floating > label {
        padding: 0.875rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Profile Header Specific Styles */
.profile-stats .stat-item {
    color: white;
}

.profile-stats .stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.profile-stats .stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

.profile-breadcrumb .breadcrumb {
    background: none;
    padding: 0;
}

.profile-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: rgba(255, 255, 255, 0.5);
    font-weight: bold;
}

.profile-breadcrumb .breadcrumb-item a {
    text-decoration: none;
    transition: all 0.3s ease;
}

.profile-breadcrumb .breadcrumb-item a:hover {
    color: white !important;
    text-decoration: underline;
}

.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
}
