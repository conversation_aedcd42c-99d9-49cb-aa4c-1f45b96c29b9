/* Responsive Header CSS */
/* Modern, mobile-first responsive navigation design */

/* Base Header Styles */
.navbar {
    background: linear-gradient(135deg, #86B6F6 0%, #6fa8f5 100%) !important;
    border-bottom: 3px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    padding: 0.75rem 1rem !important;
    min-height: 60px !important;
    position: relative !important;
    z-index: 1000 !important;
}

.navbar-brand {
    font-size: 1.5rem !important;
    font-weight: 700 !important;
    color: white !important;
    text-decoration: none !important;
    padding: 0.5rem 0 !important;
    margin-right: 2rem !important;
    transition: all 0.3s ease !important;
}

.navbar-brand:hover {
    color: rgba(255, 255, 255, 0.9) !important;
    text-decoration: none !important;
    transform: scale(1.05) !important;
}

/* Navigation Links */
.navbar-nav {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    gap: 0.5rem !important;
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
}

.navbar-nav .nav-item {
    margin: 0 !important;
}

.navbar-nav .nav-link {
    display: inline-flex !important;
    align-items: center !important;
    padding: 0.5rem 1rem !important;
    border-radius: 0.375rem !important;
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    white-space: nowrap !important;
    border: 1px solid transparent !important;
}

/* Main Navigation Buttons */
.custom-button-color {
    background: rgba(255, 255, 255, 0.15) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(10px) !important;
}

.custom-button-color:hover {
    background: rgba(255, 255, 255, 0.25) !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

/* Admin Button */
.custom-button-color-red {
    background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.custom-button-color-red:hover {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3) !important;
}

/* User Menu */
.navbar-nav:last-child {
    margin-left: auto !important;
    gap: 0.25rem !important;
}

.navbar-nav:last-child .nav-link {
    color: white !important;
    font-weight: 500 !important;
    padding: 0.375rem 0.75rem !important;
}

.navbar-nav:last-child .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 0.25rem !important;
}

/* Logout Button */
.navbar-nav form button {
    background: none !important;
    border: none !important;
    color: white !important;
    font-weight: 500 !important;
    padding: 0.375rem 0.75rem !important;
    border-radius: 0.25rem !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
}

.navbar-nav form button:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Mobile Toggle Button - Fixed for proper mobile display */
@media (max-width: 991.98px) {
    .navbar-toggler {
        display: block !important;
        background: rgba(255, 255, 255, 0.15) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        border-radius: 0.375rem !important;
        padding: 0.5rem !important;
        transition: all 0.3s ease !important;
    }

    .navbar-toggler:hover {
        background: rgba(255, 255, 255, 0.25) !important;
    }

    .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
        width: 1.5rem !important;
        height: 1.5rem !important;
    }
}

@media (min-width: 992px) {
    .navbar-toggler {
        display: none !important;
    }
}

/* Responsive Breakpoints */

/* Large Desktop (>1200px) */
@media (min-width: 1200px) {
    .navbar {
        padding: 1rem 2rem !important;
    }
    
    .navbar-brand {
        font-size: 1.75rem !important;
        margin-right: 3rem !important;
    }
    
    .navbar-nav {
        gap: 0.75rem !important;
    }
    
    .navbar-nav .nav-link {
        padding: 0.625rem 1.25rem !important;
        font-size: 0.9rem !important;
    }
}

/* Desktop (992px-1200px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .navbar {
        padding: 0.875rem 1.5rem !important;
    }
    
    .navbar-brand {
        font-size: 1.5rem !important;
        margin-right: 2rem !important;
    }
    
    .navbar-nav {
        gap: 0.5rem !important;
    }
    
    .navbar-nav .nav-link {
        padding: 0.5rem 1rem !important;
        font-size: 0.875rem !important;
    }
}

/* iPad Mini and Tablet Portrait (768px-991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .navbar {
        padding: 0.5rem !important;
        min-height: 56px !important;
        flex-wrap: wrap !important;
    }

    .navbar-brand {
        font-size: 1.125rem !important;
        margin-right: auto !important;
        flex: 1 !important;
    }

    .navbar-toggler {
        display: block !important;
        order: 2 !important;
    }

    .navbar-collapse {
        display: none !important;
        flex-basis: 100% !important;
        order: 3 !important;
        margin-top: 0.5rem !important;
        padding-top: 0.5rem !important;
        border-top: 1px solid rgba(255, 255, 255, 0.2) !important;
    }

    .navbar-collapse.show {
        display: flex !important;
        flex-direction: column !important;
        align-items: stretch !important;
    }

    .navbar-nav {
        flex-direction: column !important;
        gap: 0.5rem !important;
        width: 100% !important;
        margin-bottom: 0.5rem !important;
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 1rem !important;
        font-size: 0.875rem !important;
        text-align: center !important;
        width: 100% !important;
        border-radius: 0.5rem !important;
    }

    .navbar-nav:last-child {
        margin-left: 0 !important;
        border-top: 1px solid rgba(255, 255, 255, 0.2) !important;
        padding-top: 0.5rem !important;
        margin-top: 0.5rem !important;
    }

    .navbar-nav:last-child .nav-link {
        padding: 0.5rem 1rem !important;
        font-size: 0.875rem !important;
        text-align: center !important;
    }

    .navbar-nav form {
        width: 100% !important;
    }

    .navbar-nav form button {
        padding: 0.5rem 1rem !important;
        font-size: 0.875rem !important;
        width: 100% !important;
        text-align: center !important;
        border-radius: 0.5rem !important;
    }
}

/* Mobile Landscape (576px-768px) */
@media (min-width: 576px) and (max-width: 767px) {
    .navbar {
        padding: 0.5rem 0.75rem !important;
        min-height: 56px !important;
    }
    
    .navbar-brand {
        font-size: 1.25rem !important;
        margin-right: 1rem !important;
    }
    
    .navbar-nav {
        gap: 0.25rem !important;
    }
    
    .navbar-nav .nav-link {
        padding: 0.25rem 0.5rem !important;
        font-size: 0.75rem !important;
    }
    
    .navbar-nav:last-child .nav-link {
        padding: 0.25rem 0.375rem !important;
        font-size: 0.75rem !important;
    }
    
    .navbar-nav form button {
        padding: 0.25rem 0.375rem !important;
        font-size: 0.75rem !important;
    }
}

/* Override Bootstrap navbar-expand-lg behavior */
@media (max-width: 991px) {
    .navbar-expand-lg .navbar-collapse {
        display: none !important;
        flex-basis: 100% !important;
        flex-grow: 1 !important;
    }

    .navbar-expand-lg .navbar-collapse.show {
        display: flex !important;
        flex-direction: column !important;
    }

    .navbar-expand-lg .navbar-toggler {
        display: block !important;
    }

    .navbar-expand-lg .navbar-nav {
        flex-direction: column !important;
        width: 100% !important;
    }
}

/* Tablet Portrait and Mobile (≤768px) */
@media (max-width: 768px) {
    .navbar {
        padding: 0.5rem !important;
        min-height: 56px !important;
        flex-wrap: wrap !important;
    }

    .navbar-brand {
        font-size: 1.125rem !important;
        margin-right: auto !important;
        flex: 1 !important;
    }

    .navbar-toggler {
        display: block !important;
        order: 2 !important;
    }

    .navbar-collapse {
        display: none !important;
        flex-basis: 100% !important;
        order: 3 !important;
        margin-top: 0.5rem !important;
        padding-top: 0.5rem !important;
        border-top: 1px solid rgba(255, 255, 255, 0.2) !important;
    }

    .navbar-collapse.show {
        display: flex !important;
        flex-direction: column !important;
        align-items: stretch !important;
    }

    .navbar-nav {
        flex-direction: column !important;
        gap: 0.5rem !important;
        width: 100% !important;
        margin-bottom: 0.5rem !important;
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 1rem !important;
        font-size: 0.875rem !important;
        text-align: center !important;
        width: 100% !important;
        border-radius: 0.5rem !important;
    }

    .navbar-nav:last-child {
        margin-left: 0 !important;
        border-top: 1px solid rgba(255, 255, 255, 0.2) !important;
        padding-top: 0.5rem !important;
        margin-top: 0.5rem !important;
    }

    .navbar-nav:last-child .nav-link {
        padding: 0.5rem 1rem !important;
        font-size: 0.875rem !important;
        text-align: center !important;
    }

    .navbar-nav form {
        width: 100% !important;
    }

    .navbar-nav form button {
        padding: 0.5rem 1rem !important;
        font-size: 0.875rem !important;
        width: 100% !important;
        text-align: center !important;
        border-radius: 0.5rem !important;
    }
}

/* Mobile Portrait (≤576px) */
@media (max-width: 575px) {
    .navbar {
        padding: 0.375rem !important;
        min-height: 52px !important;
    }

    .navbar-brand {
        font-size: 1rem !important;
    }

    .navbar-nav .nav-link {
        padding: 0.625rem 0.875rem !important;
        font-size: 0.8rem !important;
    }

    .navbar-nav:last-child .nav-link,
    .navbar-nav form button {
        padding: 0.5rem 0.875rem !important;
        font-size: 0.8rem !important;
    }
}

/* iPhone 14 Pro and similar (430px) */
@media (max-width: 430px) {
    .navbar {
        padding: 0.375rem !important;
        min-height: 52px !important;
    }

    .navbar-brand {
        font-size: 1rem !important;
    }

    .navbar-nav .nav-link {
        padding: 0.625rem 0.875rem !important;
        font-size: 0.8rem !important;
    }

    .navbar-nav:last-child .nav-link,
    .navbar-nav form button {
        padding: 0.5rem 0.875rem !important;
        font-size: 0.8rem !important;
    }
}

/* Extra Small Mobile (≤360px) */
@media (max-width: 360px) {
    .navbar {
        padding: 0.25rem !important;
        min-height: 48px !important;
    }

    .navbar-brand {
        font-size: 0.9rem !important;
    }

    .navbar-nav .nav-link {
        padding: 0.5rem 0.625rem !important;
        font-size: 0.75rem !important;
    }

    .navbar-nav:last-child .nav-link,
    .navbar-nav form button {
        padding: 0.375rem 0.625rem !important;
        font-size: 0.75rem !important;
    }
}

/* High DPI Screens */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .navbar {
        border-bottom-width: 2px !important;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .navbar-brand,
    .navbar-nav .nav-link,
    .navbar-toggler,
    .navbar-nav form button {
        transition: none !important;
        transform: none !important;
    }
}

/* Focus States for Accessibility */
.navbar-brand:focus,
.navbar-nav .nav-link:focus,
.navbar-toggler:focus,
.navbar-nav form button:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8) !important;
    outline-offset: 2px !important;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .navbar {
        border-bottom: 3px solid white !important;
    }

    .navbar-nav .nav-link,
    .navbar-brand {
        border: 1px solid rgba(255, 255, 255, 0.5) !important;
    }
}

/* Scrolled State */
.navbar.scrolled {
    background: linear-gradient(135deg, rgba(134, 182, 246, 0.95) 0%, rgba(111, 168, 245, 0.95) 100%) !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

/* Screen Size Classes for JavaScript Targeting */
.navbar.screen-xs {
    min-height: 48px !important;
}

.navbar.screen-sm {
    min-height: 52px !important;
}

.navbar.screen-md {
    min-height: 56px !important;
}

.navbar.screen-lg {
    min-height: 60px !important;
}

.navbar.screen-xl {
    min-height: 64px !important;
}

/* Touch Device Optimizations */
.touch-device .navbar-nav .nav-link {
    min-height: 44px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.touch-device .navbar-toggler {
    min-width: 44px !important;
    min-height: 44px !important;
}

/* Low Performance Device Optimizations */
.low-performance .navbar-nav .nav-link,
.low-performance .navbar-brand,
.low-performance .navbar-toggler {
    transition: none !important;
    transform: none !important;
}

/* Battery Saver Mode */
.battery-saver .navbar-nav .nav-link,
.battery-saver .navbar-brand,
.battery-saver .navbar-toggler {
    transition: none !important;
    animation: none !important;
}

/* Loading State */
.navbar-loading {
    opacity: 0.7 !important;
    pointer-events: none !important;
}

/* Error State */
.navbar-error {
    background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%) !important;
}

/* Print Styles */
@media print {
    .navbar {
        display: none !important;
    }
}

/* Dark Mode Support (if implemented) */
@media (prefers-color-scheme: dark) {
    .navbar {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
        border-bottom-color: rgba(255, 255, 255, 0.1) !important;
    }

    .navbar-brand {
        color: #f9fafb !important;
    }

    .custom-button-color {
        background: rgba(255, 255, 255, 0.1) !important;
        border-color: rgba(255, 255, 255, 0.2) !important;
    }

    .custom-button-color:hover {
        background: rgba(255, 255, 255, 0.2) !important;
    }
}

/* Animation Classes */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

.navbar-collapse.show {
    animation: slideDown 0.3s ease !important;
}

.navbar-collapse.hiding {
    animation: slideUp 0.3s ease !important;
}

/* Utility Classes */
.navbar-sticky {
    position: sticky !important;
    top: 0 !important;
    z-index: 1030 !important;
}

.navbar-fixed {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1030 !important;
}

/* Responsive Text Utilities */
@media (max-width: 360px) {
    .navbar-text-responsive {
        font-size: 0.7rem !important;
    }
}

@media (min-width: 361px) and (max-width: 575px) {
    .navbar-text-responsive {
        font-size: 0.75rem !important;
    }
}

@media (min-width: 576px) and (max-width: 767px) {
    .navbar-text-responsive {
        font-size: 0.8rem !important;
    }
}

@media (min-width: 768px) {
    .navbar-text-responsive {
        font-size: 0.875rem !important;
    }
}
