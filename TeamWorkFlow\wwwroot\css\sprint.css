/* Sprint To Do - GitHub RoadMap Style CSS */

.sprint-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Sprint Header */
.sprint-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e1e4e8;
}

.sprint-title h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 600;
    color: #24292e;
    display: flex;
    align-items: center;
}

.sprint-subtitle {
    margin: 5px 0 0 0;
    color: #586069;
    font-size: 1rem;
}

.sprint-actions {
    display: flex;
    gap: 10px;
}

.sprint-actions .btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 8px 16px;
    transition: all 0.2s ease;
}

/* Capacity Overview */
.capacity-overview {
    margin-bottom: 30px;
}

.capacity-card {
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 8px;
    padding: 20px;
}

.capacity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.capacity-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #24292e;
}

.capacity-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.capacity-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 15px;
}

.metric {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e1e4e8;
}

.metric-label {
    font-size: 0.875rem;
    color: #586069;
    margin-bottom: 5px;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #24292e;
    margin-bottom: 8px;
}

.metric-bar {
    height: 6px;
    background: #e1e4e8;
    border-radius: 3px;
    overflow: hidden;
}

.metric-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
    transition: width 0.3s ease;
}

.capacity-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 12px;
    color: #856404;
    display: flex;
    align-items: center;
    gap: 8px;
}

.capacity-warning i {
    color: #f39c12;
}

/* Sprint Board */
.sprint-board {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    min-height: 600px;
}

.sprint-column {
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 8px;
    overflow: hidden;
}

.column-header {
    background: white;
    padding: 20px;
    border-bottom: 1px solid #e1e4e8;
}

.column-header h3 {
    margin: 0 0 10px 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #24292e;
    display: flex;
    align-items: center;
    gap: 8px;
}

.column-stats {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.stat {
    font-size: 0.875rem;
    color: #586069;
    background: #f6f8fa;
    padding: 4px 8px;
    border-radius: 4px;
}

.column-filters {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.filter-row {
    display: flex;
    gap: 10px;
}

.filter-row .form-control,
.filter-row .form-select {
    border-radius: 6px;
    border: 1px solid #d0d7de;
    font-size: 0.875rem;
}

/* Task List */
.task-list {
    padding: 20px;
    min-height: 400px;
    max-height: 600px;
    overflow-y: auto;
}

.task-card {
    background: white;
    border: 1px solid #d0d7de;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    cursor: grab;
    transition: all 0.2s ease;
    position: relative;
}

.task-card:hover {
    border-color: #0969da;
    box-shadow: 0 2px 8px rgba(9, 105, 218, 0.1);
}

.task-card.sortable-ghost {
    opacity: 0.5;
    transform: rotate(2deg);
}

.task-card.sortable-chosen {
    cursor: grabbing;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.task-title {
    flex: 1;
}

.task-name {
    font-weight: 600;
    color: #24292e;
    font-size: 1rem;
    display: block;
    margin-bottom: 4px;
}

.task-project {
    font-size: 0.875rem;
    color: #656d76;
    font-family: 'SFMono-Regular', Consolas, monospace;
}

.task-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.task-card:hover .task-actions {
    opacity: 1;
}

.btn-icon {
    background: none;
    border: none;
    padding: 4px;
    border-radius: 4px;
    color: #656d76;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    background: #f6f8fa;
    color: #24292e;
}

.add-task-btn:hover {
    background: #dafbe1;
    color: #137333;
}

.remove-task-btn:hover {
    background: #ffebe9;
    color: #cf222e;
}

.task-meta {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
    flex-wrap: wrap;
}

.task-status,
.task-priority,
.task-duration {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 2px 8px;
    border-radius: 12px;
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.task-duration {
    background: #656d76;
}

.task-machine,
.task-operators,
.task-timeline,
.task-deadline {
    font-size: 0.875rem;
    color: #656d76;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.task-machine i,
.task-operators i,
.task-timeline i,
.task-deadline i {
    width: 12px;
    color: #8c959f;
}

.timeline-status {
    margin-left: auto;
    font-weight: 500;
}

.task-description {
    font-size: 0.875rem;
    color: #656d76;
    line-height: 1.4;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.task-time-editor {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e1e4e8;
}

.task-time-editor input {
    width: 80px;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #656d76;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state p {
    margin: 8px 0;
}

/* Pagination */
.pagination-container {
    padding: 20px;
    border-top: 1px solid #e1e4e8;
    background: white;
}

.pagination {
    justify-content: center;
    margin: 0;
}

.page-link {
    color: #0969da;
    border-color: #d0d7de;
    border-radius: 6px;
    margin: 0 2px;
}

.page-link:hover {
    background-color: #f6f8fa;
    border-color: #d0d7de;
}

.page-item.active .page-link {
    background-color: #0969da;
    border-color: #0969da;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sprint-board {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .sprint-header {
        flex-direction: column;
        gap: 20px;
        align-items: flex-start;
    }
    
    .capacity-metrics {
        grid-template-columns: 1fr;
    }
    
    .filter-row {
        flex-direction: column;
    }
    
    .task-header {
        flex-direction: column;
        gap: 8px;
    }
    
    .task-actions {
        opacity: 1;
        align-self: flex-end;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #e1e4e8;
    border-top-color: #0969da;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Drag and Drop Indicators */
.sortable-drag {
    opacity: 0.8;
    transform: rotate(5deg);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.drop-zone-active {
    background: #dafbe1;
    border: 2px dashed #28a745;
}

.drop-zone-invalid {
    background: #ffebe9;
    border: 2px dashed #dc3545;
}
