﻿h1, h2, hr, footer > div {
    color: white;
}

.index-page-greeting {
    font-weight: 700;
}

.index-page-greeting-p {
    color: white;
}

header {
    background-color: #EEF5FF;
    color: white;
}

.form-label-add-edit {
    margin-bottom: 0.5rem;
    color: white;
}

section, section > div > p > a, section > form > div > p > a {
    color: white;
}

.form-floating {
    color: grey;
}

.home-page-image {
    border-radius: 20px;
}


.btn-purple {
    background-color: purple;
    color: white;
    border-color: purple;
}

.text-purple { color: purple; 

}

.custom-button-color-red {
    background-color: red;
    border-color: red;
}

/* Navigation fixes */
.navbar-nav {
    display: flex !important;
    flex-direction: row !important;
}

.navbar-nav .nav-item {
    display: list-item !important;
}

.navbar-nav .nav-link {
    display: block !important;
    padding: 0.5rem 1rem !important;
    color: #212529 !important;
    text-decoration: none !important;
}

.navbar-collapse {
    display: flex !important;
    justify-content: space-between !important;
}

/* Ensure login/register buttons are visible */
.navbar-nav:last-child {
    margin-left: auto !important;
}

/* Navigation styling is now handled in enhanced-navigation.css for better visibility */
/* This comment replaces the old conflicting styles */

.navbar .navbar-nav .nav-item .nav-link:hover {
    color: #0056b3 !important;
}



