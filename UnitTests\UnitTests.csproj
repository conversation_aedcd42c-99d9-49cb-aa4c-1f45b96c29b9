<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
    <UserSecretsId>b7e8b97f-ce89-4ea2-82f7-4abcb48edd93</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="3.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="6.0.27" />
    <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="6.0.1" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.5.0" />
    <PackageReference Include="Moq" Version="4.20.70" />
    <PackageReference Include="Moq.EntityFrameworkCore" Version="6.0.1.4" />
    <PackageReference Include="NUnit" Version="3.13.3" />
    <PackageReference Include="NUnit.Analyzers" Version="3.6.1" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.4.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TeamWorkFlow.Core\TeamWorkFlow.Core.csproj" />
    <ProjectReference Include="..\TeamWorkFlow.Infrastructure\TeamWorkFlow.Infrastructure.csproj" />
    <ProjectReference Include="..\TeamWorkFlow\TeamWorkFlow.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="NUnit.Framework" />
  </ItemGroup>

</Project>
